# 样本标注修正功能实现

## 🎯 功能概述

将 `sample.vue` 中的"显示标注/隐藏标注"按钮功能修改为类似 `detected.vue` 中的"修正"功能，允许用户对样本的标注进行可视化编辑和修正。

## 🔄 主要修改

### 1. 按钮功能替换

**修改前:**
```html
<el-button size="small" :type="showAnnotations ? 'primary' : 'default'" @click="toggleAnnotations"
  :disabled="!currentImage || !currentAnnotations.length">
  {{ showAnnotations ? '隐藏标注' : '显示标注' }}
</el-button>
```

**修改后:**
```html
<el-button size="small" type="warning" @click="editSampleAnnotations" 
  :disabled="!currentImage || !currentSample" class="action-btn">
  修正
</el-button>
```

### 2. 新增数据结构

```javascript
// 标注修正功能状态
showModifyDialog: false,        // 修正对话框显示状态
modifyingSample: null,          // 正在修正的样本
modifyAnnotations: [],          // 修正中的标注数据
selectedAnnotation: null,       // 选中的标注框
modifyCanvas: null,             // 修正canvas元素
modifyContext: null,            // canvas上下文
modifyImage: null,              // 修正图像
isDragging: false,              // 是否正在拖拽
isCreatingBox: false,           // 是否正在创建新框
dragStart: { x: 0, y: 0 },      // 拖拽起始位置
newBoxStart: { x: 0, y: 0 },    // 新框起始位置
```

### 3. 修正对话框界面

#### 左侧 - 图像编辑区域
- **图像显示**: 自适应canvas显示原图
- **标注框绘制**: 实时显示所有标注框
- **交互功能**: 
  - 点击选择标注框
  - 拖拽移动标注框
  - 鼠标绘制新标注框
- **工具栏**: 添加、删除、清空标注框

#### 右侧 - 标注管理面板
- **标注列表**: 显示所有标注框信息
  - 标注索引和类别
  - 中心坐标和尺寸
  - 颜色标识
- **属性编辑**: 选中标注框的详细属性
  - 类别ID调整
  - 精确坐标编辑
  - 尺寸微调

### 4. 核心功能方法

#### 主要方法列表
```javascript
editSampleAnnotations()        // 开始编辑标注
initializeModifyCanvas()       // 初始化修正canvas
loadModifyImage()              // 加载修正图像
drawModifyCanvas()             // 绘制修正canvas
drawModifyAnnotations()        // 绘制标注框

// 鼠标交互
handleModifyMouseDown()        // 鼠标按下事件
handleModifyMouseMove()        // 鼠标移动事件
handleModifyMouseUp()          // 鼠标释放事件

// 标注操作
addNewAnnotation()             // 添加新标注框
deleteSelectedAnnotation()     // 删除选中标注框
clearAllAnnotations()          // 清空所有标注框
selectAnnotation()             // 选择标注框
createNewAnnotation()          // 创建新标注框

// 对话框管理
closeModifyDialog()            // 关闭修正对话框
saveModifiedAnnotations()      // 保存修改的标注
```

#### 坐标转换逻辑
```javascript
// YOLO相对坐标 ↔ Canvas像素坐标转换
const centerX = offsetX + xCenter * drawWidth
const centerY = offsetY + yCenter * drawHeight
const boxWidth = width * drawWidth
const boxHeight = height * drawHeight
```

### 5. 交互功能详解

#### 🖱️ 鼠标操作
- **点击空白区域**: 开始绘制新标注框
- **点击标注框**: 选中该标注框
- **拖拽标注框**: 移动标注框位置
- **拖拽空白区域**: 创建新标注框

#### ⌨️ 标注框管理
- **添加**: 点击"添加标注框"按钮或鼠标绘制
- **选择**: 点击标注框或在列表中点击
- **移动**: 拖拽选中的标注框
- **删除**: 选中后点击"删除选中"按钮
- **清空**: 点击"清空所有"按钮

#### 📝 属性编辑
- **类别ID**: 数字输入框调整 (0-99)
- **中心坐标**: 精确到0.001的相对坐标
- **尺寸**: 宽度和高度的相对值

### 6. 视觉设计

#### 🎨 标注框样式
- **普通状态**: 2px边框，半透明填充
- **选中状态**: 3px黄色边框，高亮填充
- **颜色系统**: 基于类别ID的颜色映射
- **标签显示**: 类别ID标签在框的左上角

#### 🖼️ 界面布局
- **左右分栏**: 图像编辑区 + 标注管理区
- **响应式设计**: 自适应不同屏幕尺寸
- **现代UI**: Element Plus组件风格
- **交互反馈**: 悬停、选中状态变化

### 7. 数据格式

#### YOLO格式标注
```
classId xCenter yCenter width height
```
- `classId`: 类别标识符 (整数)
- `xCenter`: 中心X坐标 (0-1相对值)
- `yCenter`: 中心Y坐标 (0-1相对值)  
- `width`: 宽度 (0-1相对值)
- `height`: 高度 (0-1相对值)

#### 内部数据结构
```javascript
{
  xCenter: 0.5,      // 中心X坐标
  yCenter: 0.5,      // 中心Y坐标
  width: 0.2,        // 宽度
  height: 0.2,       // 高度
  classId: 0,        // 类别ID
  color: '#ff0000'   // 显示颜色
}
```

### 8. 保存机制

#### 当前实现
- 保存到前端状态 (`currentAnnotations`)
- 更新主界面显示
- 生成YOLO格式字符串

#### 扩展接口
```javascript
// TODO: 实现保存标注到后端的API调用
// 可以调用类似的API:
// await APIUpdateSampleAnnotation({
//   sampleId: this.modifyingSample.id,
//   annotationContent: yoloContent
// })
```

### 9. 用户体验优化

#### ✅ 交互友好性
- 实时预览标注框绘制
- 拖拽时的平滑移动
- 选中状态的视觉反馈
- 操作的撤销确认

#### ✅ 数据安全性
- 深拷贝避免意外修改
- 坐标边界检查
- 最小尺寸限制
- 操作前确认提示

#### ✅ 性能优化
- Canvas重绘优化
- 事件节流处理
- 内存清理机制
- 响应式布局

### 10. 与原功能的对比

| 功能 | 修改前 | 修改后 |
|------|--------|--------|
| 按钮文本 | "显示标注/隐藏标注" | "修正" |
| 按钮类型 | 切换型 | 操作型 |
| 主要功能 | 显示/隐藏现有标注 | 编辑和修正标注 |
| 交互方式 | 简单切换 | 复杂编辑界面 |
| 数据修改 | 只读显示 | 可编辑修改 |
| 用户控制 | 基础查看 | 完整编辑能力 |

### 11. 技术特点

#### 🔧 技术栈
- **Vue 3**: Composition API
- **Element Plus**: UI组件库
- **Canvas API**: 图像和标注绘制
- **JavaScript**: ES6+语法

#### 🎯 设计模式
- **组件化**: 模块化的方法组织
- **响应式**: Vue响应式数据绑定
- **事件驱动**: 鼠标事件处理机制
- **状态管理**: 集中的状态控制

#### 🚀 性能考虑
- **按需渲染**: 只在需要时重绘canvas
- **事件优化**: 防抖和节流处理
- **内存管理**: 及时清理资源
- **坐标缓存**: 避免重复计算

## 🎉 总结

通过这次修改，`sample.vue` 中的标注功能从简单的显示/隐藏切换升级为功能完整的标注编辑器，用户现在可以：

1. **可视化编辑**: 直接在图像上操作标注框
2. **精确调整**: 通过数值输入精确控制坐标
3. **批量管理**: 添加、删除、清空标注框
4. **实时预览**: 所见即所得的编辑体验
5. **数据保存**: 将修改保存到系统中

这个实现参考了 `detected.vue` 中的修正功能设计，提供了一致的用户体验和强大的编辑能力。
