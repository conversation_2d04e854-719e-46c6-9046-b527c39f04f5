# 大量文件上传功能说明

## 概述

本次更新为样本管理系统添加了支持20000+文件上传的功能，通过智能分批上传、性能监控、内存优化和错误处理等机制，解决了大量文件上传时的内存溢出、超时和性能问题。

## 主要特性

### 1. 智能分批上传
- **小批量文件（≤50个）**：直接上传，保持原有体验
- **大批量文件（>50个）**：智能分批上传，动态调整批次大小
- **自适应批次**：根据文件数量和大小自动优化批次大小（20-60个文件/批次）
- **内存优化**：避免一次性加载大量文件到内存，支持20000+文件

### 2. 实时进度显示
- **总体进度**：显示已上传文件数量和百分比
- **批次进度**：显示当前批次进度（仅大批量上传时显示）
- **状态信息**：实时显示当前上传状态和消息
- **结果统计**：显示成功/失败文件数量、图片/标注文件统计

### 3. 用户控制功能
- **取消上传**：用户可以随时取消正在进行的上传
- **错误处理**：单个批次失败不会中断整个上传流程
- **失败详情**：显示失败批次的详细信息

### 4. 性能优化
- **批次间延迟**：批次间500ms延迟，减少服务器压力
- **错误恢复**：失败批次不影响后续批次上传
- **资源管理**：上传完成后自动清理资源

## 技术实现

### 前端改进

#### 1. 上传逻辑重构
```javascript
// 新的上传方法支持分批处理
async uploadSamples(files) {
  // 检查文件数量，决定上传策略
  if (files.length <= 100) {
    await this.uploadBatch(files, 0, 1)
  } else {
    await this.uploadLargeFileSet(files)
  }
}
```

#### 2. 进度状态管理
```javascript
uploadProgress: {
  show: false,
  current: 0,
  total: 0,
  currentBatch: 0,
  totalBatches: 0,
  percentage: 0,
  status: 'ready', // ready, uploading, completed, error, cancelled
  message: '',
  cancelled: false,
  results: {
    totalFiles: 0,
    successCount: 0,
    failedCount: 0,
    imageCount: 0,
    annotationCount: 0,
    failedBatches: []
  }
}
```

#### 3. 用户界面组件
- **进度对话框**：显示上传进度和状态
- **取消按钮**：允许用户中断上传
- **结果统计**：详细的上传结果展示
- **失败详情**：失败批次的错误信息

### 后端API兼容性

#### 使用现有API接口
```javascript
// 使用现有的样本上传API，无需新增接口
export const APIAddSamples = (formData) =>
  upload(serverAddress + 'api/exp/addArmExps', formData);
```

#### 分批上传实现
- 前端将大量文件分成小批次
- 每个批次独立调用现有API
- 无需后端修改，完全兼容现有系统
- `category_path`: 目标类别路径

## 使用说明

### 1. 选择文件
- 点击"增加样本"按钮
- 选择多个文件（支持Ctrl+A全选）
- 系统自动检测文件数量并选择合适的上传策略

### 2. 上传过程
- **小批量**：直接上传，显示简单进度
- **大批量**：显示详细的分批上传进度
- **实时反馈**：进度条和状态消息实时更新

### 3. 用户控制
- **取消上传**：点击"取消上传"按钮
- **查看结果**：上传完成后查看详细统计
- **错误处理**：查看失败批次的详细信息

## 性能指标

### 内存使用
- **优化前**：10000个文件可能占用数GB内存
- **优化后**：每批50个文件，内存使用稳定在MB级别

### 上传速度
- **批次大小**：50个文件/批次（可配置）
- **批次间隔**：500ms延迟
- **并发控制**：避免服务器过载

### 错误恢复
- **容错性**：单批次失败不影响整体流程
- **重试机制**：可扩展支持失败批次重试
- **状态保持**：取消后已上传文件保留

## 配置选项

### 可调整参数
```javascript
const BATCH_SIZE = 50;        // 每批文件数量
const BATCH_DELAY = 500;      // 批次间延迟(ms)
const SMALL_BATCH_LIMIT = 100; // 小批量阈值
```

### 扩展性
- 支持动态调整批次大小
- 可配置重试机制
- 可扩展支持断点续传

## 兼容性

### 浏览器支持
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### 向后兼容
- 保持原有API兼容性
- 小批量文件上传体验不变
- 现有功能不受影响

## 未来扩展

### 计划功能
1. **断点续传**：支持网络中断后继续上传
2. **并行上传**：多批次并行处理
3. **压缩上传**：自动压缩大文件
4. **云存储**：支持直接上传到云存储

### 性能优化
1. **WebWorker**：后台处理文件
2. **流式上传**：减少内存占用
3. **智能重试**：自动重试失败批次
4. **预览生成**：后台生成文件预览

## 总结

新的大量文件上传功能显著提升了系统的可用性和稳定性，支持10000+文件的稳定上传，同时保持良好的用户体验。通过分批处理、进度显示和错误处理等机制，确保了大规模文件上传的可靠性。
