#!/usr/bin/env python3
"""
测试标注更新API的脚本
"""

import requests
import json

# 配置
BASE_URL = "http://127.0.0.1:8083"  # 根据实际后端地址调整
API_ENDPOINT = f"{BASE_URL}/api/exp/updateArmExpAnnotation"

def test_update_annotation():
    """测试更新标注接口"""
    
    # 测试数据
    test_data = {
        "id": 1,  # 替换为实际的样本ID
        "annotation_content": "0 0.5 0.5 0.2 0.3\n1 0.3 0.7 0.15 0.25"  # YOLO格式标注
    }
    
    try:
        print("正在测试标注更新API...")
        print(f"请求URL: {API_ENDPOINT}")
        print(f"请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
        
        # 发送POST请求
        response = requests.post(
            API_ENDPOINT,
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("status") == "success":
                print("✅ API测试成功！标注更新功能正常工作")
                return True
            else:
                print(f"❌ API返回错误: {result.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"错误详情: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
            except:
                print(f"错误详情: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误：无法连接到后端服务器")
        print("请确保后端服务器正在运行")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_get_sample():
    """测试获取样本接口，用于验证样本是否存在"""
    
    sample_id = 1  # 替换为实际的样本ID
    get_url = f"{BASE_URL}/api/exp/viewArmExp"
    
    try:
        print(f"\n正在测试获取样本API (ID: {sample_id})...")
        
        response = requests.get(
            get_url,
            params={"id": sample_id},
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 样本获取成功")
            sample_data = result.get("data", {}).get("sample", {})
            print(f"样本信息: ID={sample_data.get('id')}, 名称={sample_data.get('name')}")
            return True
        else:
            print(f"❌ 获取样本失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 获取样本测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("=== 标注更新API测试 ===\n")
    
    # 首先测试样本是否存在
    sample_exists = test_get_sample()
    
    if sample_exists:
        # 如果样本存在，测试更新标注
        success = test_update_annotation()
    else:
        print("\n⚠️  样本不存在，请先创建样本或修改测试脚本中的样本ID")
        success = False
    
    print(f"\n=== 测试结果: {'通过' if success else '失败'} ===")
