<template>
  <div class="map-container">
    <!-- Leaflet Map Container -->
    <div id="leaflet-map" ref="mapContainer" class="leaflet-map"></div>

    <!-- Layer Control -->
    <div class="layer-controls">
      <button
        @click="switchToSatellite"
        :class="{ active: currentLayer === 'satellite' }"
        class="layer-btn"
      >
        卫星图像
      </button>
      <button
        @click="switchToStreet"
        :class="{ active: currentLayer === 'street' }"
        class="layer-btn"
      >
        电子地图
      </button>
    </div>

    <!-- Coordinates Display -->
    <div class="coordinates-display">
      <div class="coord-item">
        <span class="coord-label">纬度:</span>
        <span class="coord-value">{{ mouseCoordinates.lat.toFixed(6) }}°</span>
      </div>
      <div class="coord-item">
        <span class="coord-label">经度:</span>
        <span class="coord-value">{{ mouseCoordinates.lng.toFixed(6) }}°</span>
      </div>
      <div class="coord-hint">
        <span class="hint-text">鼠标位置坐标</span>
        <span class="click-hint">点击复制</span>
      </div>
    </div>
  </div>
</template>

<script>
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

// Fix for default markers in Leaflet with webpack
delete L.Icon.Default.prototype._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: require('leaflet/dist/images/marker-icon-2x.png'),
  iconUrl: require('leaflet/dist/images/marker-icon.png'),
  shadowUrl: require('leaflet/dist/images/marker-shadow.png'),
})

export default {
  name: 'MapComponent',
  props: {
    gpsCoordinates: {
      type: Object,
      default: () => ({ lat: 39.9042, lng: 116.4074 })
    },
    markersLocked: {
      type: Boolean,
      default: false
    },
    flightPath: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      map: null,
      currentLayer: 'satellite',
      satelliteLayer: null,
      streetLayer: null,
      currentMarker: null,
      historicalMarkers: [],
      flightPathPolyline: null,
      customIcon: null,
      historicalIcon: null,
      mouseCoordinates: {
        lat: 39.9042,
        lng: 116.4074
      }
    }
  },
  watch: {
    gpsCoordinates: {
      handler(newCoords, oldCoords) {
        if (this.map && newCoords) {
          // Update current marker position
          if (this.currentMarker) {
            this.currentMarker.setLatLng([newCoords.lat, newCoords.lng])
          }

          // Add historical marker if not locked
          if (oldCoords && !this.markersLocked) {
            this.addHistoricalMarker(oldCoords)
          }

          // Center map on new coordinates
          this.map.setView([newCoords.lat, newCoords.lng], this.map.getZoom())
        }
      },
      deep: true
    },
    flightPath: {
      handler(newPath) {
        this.updateFlightPath(newPath)
      },
      deep: true
    }
  },
  mounted() {
    this.initializeMap()
  },
  beforeUnmount() {
    if (this.map) {
      this.map.remove()
    }
  },
  methods: {
    initializeMap() {
      // Initialize the map
      this.map = L.map(this.$refs.mapContainer, {
        center: [this.gpsCoordinates.lat, this.gpsCoordinates.lng],
        zoom: 13,
        zoomControl: true
      })

      // Create custom icons
      this.createCustomIcons()

      // Initialize layers
      this.initializeLayers()

      // Add initial marker
      this.addCurrentMarker()

      // Initialize flight path
      this.updateFlightPath(this.flightPath)

      // Add mouse move event listener to track cursor position
      this.setupMouseTracking()
    },

    createCustomIcons() {
      // Current position icon (red marker with pulse effect)
      this.customIcon = L.divIcon({
        className: 'custom-marker',
        html: `
          <div class="marker-container ${this.markersLocked ? 'locked' : ''}">
            <div class="marker-icon">📍</div>
            <div class="marker-pulse"></div>
          </div>
        `,
        iconSize: [30, 30],
        iconAnchor: [15, 15]
      })

      // Historical marker icon
      this.historicalIcon = L.divIcon({
        className: 'historical-marker',
        html: '<div class="historical-icon">📌</div>',
        iconSize: [20, 20],
        iconAnchor: [10, 10]
      })
    },

    initializeLayers() {
      // Satellite layer (default)
      this.satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
        attribution: '&copy; <a href="https://www.esri.com/">Esri</a>',
        maxZoom: 18
      })

      // Street layer
      this.streetLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 18
      })

      // Add default layer (satellite)
      this.satelliteLayer.addTo(this.map)
    },

    addCurrentMarker() {
      if (this.currentMarker) {
        this.map.removeLayer(this.currentMarker)
      }

      this.currentMarker = L.marker([this.gpsCoordinates.lat, this.gpsCoordinates.lng], {
        icon: this.customIcon
      }).addTo(this.map)

      this.currentMarker.on('click', () => {
        this.$emit('marker-click', this.gpsCoordinates)
      })
    },

    addHistoricalMarker(coords) {
      const marker = L.marker([coords.lat, coords.lng], {
        icon: this.historicalIcon
      }).addTo(this.map)

      this.historicalMarkers.push(marker)

      // Keep only last 10 historical markers
      if (this.historicalMarkers.length > 10) {
        const oldMarker = this.historicalMarkers.shift()
        this.map.removeLayer(oldMarker)
      }
    },

    updateFlightPath(path) {
      if (this.flightPathPolyline) {
        this.map.removeLayer(this.flightPathPolyline)
      }

      if (path && path.length > 1) {
        const latLngs = path.map(point => [point.lat, point.lng])
        this.flightPathPolyline = L.polyline(latLngs, {
          color: '#667eea',
          weight: 3,
          opacity: 0.8,
          dashArray: '5, 5'
        }).addTo(this.map)
      }
    },

    setupMouseTracking() {
      // Add mousemove event listener to track cursor position
      this.map.on('mousemove', (e) => {
        this.mouseCoordinates = {
          lat: e.latlng.lat,
          lng: e.latlng.lng
        }

        // Add visual feedback for coordinate updates
        this.highlightCoordinateDisplay()
      })

      // Initialize mouse coordinates with map center
      this.mouseCoordinates = {
        lat: this.gpsCoordinates.lat,
        lng: this.gpsCoordinates.lng
      }

      // Add mouseout event to handle when mouse leaves the map
      this.map.on('mouseout', () => {
        // Keep the last known coordinates when mouse leaves
        // Or you could reset to GPS coordinates if preferred
        // this.mouseCoordinates = {
        //   lat: this.gpsCoordinates.lat,
        //   lng: this.gpsCoordinates.lng
        // }
      })

      // Add click event to copy coordinates
      this.map.on('click', (e) => {
        const coords = `${e.latlng.lat.toFixed(6)}, ${e.latlng.lng.toFixed(6)}`
        navigator.clipboard.writeText(coords).then(() => {
          this.$emit('coordinates-copied', coords)
        }).catch(() => {
          // Fallback for older browsers
          console.log('Coordinates:', coords)
        })
      })
    },

    highlightCoordinateDisplay() {
      // Add a brief highlight effect to show coordinates are updating
      const coordDisplay = document.querySelector('.coordinates-display')
      if (coordDisplay) {
        coordDisplay.classList.add('updating')
        setTimeout(() => {
          coordDisplay.classList.remove('updating')
        }, 100)
      }
    },

    switchToSatellite() {
      if (this.currentLayer !== 'satellite') {
        this.map.removeLayer(this.streetLayer)
        this.map.addLayer(this.satelliteLayer)
        this.currentLayer = 'satellite'
      }
    },

    switchToStreet() {
      if (this.currentLayer !== 'street') {
        this.map.removeLayer(this.satelliteLayer)
        this.map.addLayer(this.streetLayer)
        this.currentLayer = 'street'
      }
    },

    clearScreen() {
      // Remove all historical markers
      this.historicalMarkers.forEach(marker => {
        this.map.removeLayer(marker)
      })
      this.historicalMarkers = []

      // Remove flight path
      if (this.flightPathPolyline) {
        this.map.removeLayer(this.flightPathPolyline)
        this.flightPathPolyline = null
      }
    }
  }
}
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.leaflet-map {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

/* Layer Controls */
.layer-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  z-index: 1000;
}

.layer-btn {
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #dee2e6;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

.layer-btn:hover {
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.layer-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

/* Coordinates Display */
.coordinates-display {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(255, 255, 255, 0.95);
  padding: 8px 12px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  z-index: 1000;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(0, 123, 255, 0.2);
}

.coord-item {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  margin-bottom: 2px;
}

.coord-item:last-child {
  margin-bottom: 4px;
}

.coord-label {
  font-weight: 600;
  color: #495057;
}

.coord-value {
  color: #007bff;
  font-family: monospace;
  font-weight: 600;
  transition: all 0.2s ease;
}

.coordinates-display.updating {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  border-color: rgba(0, 123, 255, 0.5);
}

.coordinates-display.updating .coord-value {
  color: #0056b3;
  text-shadow: 0 0 4px rgba(0, 123, 255, 0.3);
}

.coord-hint {
  border-top: 1px solid rgba(0, 123, 255, 0.2);
  padding-top: 4px;
  margin-top: 2px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.hint-text {
  font-size: 10px;
  color: #6c757d;
  font-style: italic;
  display: flex;
  align-items: center;
  gap: 4px;
}

.hint-text::before {
  content: "🖱️";
  font-size: 8px;
}

.click-hint {
  font-size: 9px;
  color: #007bff;
  font-weight: 600;
  opacity: 0.7;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.click-hint:hover {
  opacity: 1;
}

.click-hint::before {
  content: "📋";
  margin-right: 2px;
  font-size: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .coordinates-display {
    font-size: 10px;
    padding: 6px 8px;
  }

  .hint-text {
    font-size: 9px;
  }

  .hint-text::before {
    font-size: 7px;
  }

  .click-hint {
    font-size: 8px;
  }

  .click-hint::before {
    font-size: 7px;
  }

  .layer-controls {
    top: 5px;
    right: 5px;
  }

  .layer-btn {
    padding: 6px 8px;
    font-size: 11px;
  }
}
</style>

<!-- Global styles for custom markers -->
<style>
/* Custom marker styles (not scoped) */
.custom-marker {
  background: transparent;
  border: none;
}

.marker-container {
  position: relative;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.marker-container.locked {
  filter: hue-rotate(120deg);
}

.marker-icon {
  font-size: 24px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: bounce 2s infinite;
  z-index: 2;
}

.marker-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  background: rgba(255, 0, 0, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 2s infinite;
  z-index: 1;
}

.historical-marker {
  background: transparent;
  border: none;
}

.historical-icon {
  font-size: 16px;
  color: #6c757d;
  opacity: 0.7;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

/* Leaflet control customization */
.leaflet-control-zoom {
  border: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.leaflet-control-zoom a {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid #dee2e6 !important;
  color: #333 !important;
  font-weight: bold !important;
}

.leaflet-control-zoom a:hover {
  background: white !important;
}
</style>