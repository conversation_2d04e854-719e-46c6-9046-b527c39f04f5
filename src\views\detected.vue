<template>
  <div class="detected">
    <HeaderComponent title="智能判读" :showBackButton="true" @back="goBack" />
    <div class="main-content" :class="{ 'realtime-layout': selectedInputType === 'realtime' }">
      <!-- 1. Data Input Area (Top Left) -->
      <div class="section data-input-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="icon">📁</i>
            数据输入
          </h3>
          <!-- Input Controls in Header - right aligned -->
          <div class="header-controls">
            <div class="input-type-selector">
              <label class="control-label">输入类型:</label>
              <el-select v-model="selectedInputType" @change="selectInputType" class="type-select">
                <el-option v-for="type in inputTypes" :key="type.value" :label="type.label" :value="type.value">
                  <span class="option-content">
                    <i class="option-icon">{{ type.icon }}</i>
                    {{ type.label }}
                  </span>
                </el-option>
              </el-select>
            </div>

            <div class="upload-controls">
              <!-- Upload Image -->
              <div v-if="selectedInputType === 'image'" class="upload-group">
                <el-button type="primary" @click="triggerFileUpload('image')" class="upload-btn">
                  <i class="btn-icon">🖼️</i>
                  选择图像
                </el-button>
                <input ref="imageInput" type="file" accept="image/*" @change="handleImageUpload" style="display: none;">
                <span v-if="selectedImage" class="file-status">已选择</span>
              </div>

              <!-- Upload Folder -->
              <div v-if="selectedInputType === 'folder'" class="upload-group">
                <el-button type="primary" @click="triggerFileUpload('folder')" class="upload-btn">
                  <i class="btn-icon">📁</i>
                  选择文件夹
                </el-button>
                <input ref="folderInput" type="file" webkitdirectory @change="handleFolderUpload"
                  style="display: none;">
                <span v-if="previewsrclist.length > 0" class="file-status">{{ previewsrclist.length }} 个文件</span>
              </div>

              <!-- Upload Video -->
              <div v-if="selectedInputType === 'video'" class="upload-group">
                <el-button type="primary" @click="triggerFileUpload('video')" class="upload-btn">
                  <i class="btn-icon">🎥</i>
                  选择视频
                </el-button>
                <input ref="videoInput" type="file" accept="video/*" @change="handleVideoUpload" style="display: none;">
                <span v-if="selectedVideo" class="file-status">已选择</span>
              </div>

              <!-- Real-time Video -->
              <div v-if="selectedInputType === 'realtime'" class="upload-group">
                <el-button :type="rtspConnectionStatus === 'connected' ? 'success' : 'warning'" @click="openRtspModal"
                  class="upload-btn" :loading="rtspConnectionStatus === 'connecting'">
                  <i class="btn-icon">📡</i>
                  {{ rtspConnectionStatus === 'connected' ? '重新配置RTSP' : '配置RTSP流' }}
                </el-button>
                <span v-if="rtspConnectionStatus === 'connected'" class="file-status connected">
                  <i class="status-icon">✓</i>
                  已连接
                </span>
                <span v-else-if="rtspConnectionStatus === 'error'" class="file-status error">
                  <i class="status-icon">✗</i>
                  连接失败
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="section-content">
          <!-- Full Size Preview Area -->
          <div class="full-preview-area">
            <!-- Image Preview -->
            <div v-if="selectedInputType === 'image' && selectedImage" class="full-preview-container">
              <div class="preview-image-wrapper" @click="openImagePreview">
                <img :src="selectedImage" alt="Selected Image" class="full-preview-image" title="点击放大查看">
                <div class="image-overlay">
                  <div class="overlay-content">
                    <i class="zoom-icon">🔍</i>
                    <span>点击放大</span>
                  </div>
                </div>
              </div>
              <div class="image-actions-bar">
                <div class="file-info">
                  <span class="file-name">{{ getFileName(selectedImage) }}</span>
                  <span v-if="selectedImageFile" class="file-size">{{ formatFileSize(selectedImageFile.size) }}</span>
                  <!-- <span v-if="getCurrentImageType()" class="image-type" :class="getCurrentImageType()">
                    {{ getCurrentImageType() === 'infrared' ? '红外图像' : '可见光图像' }}
                  </span> -->
                </div>
                <el-button size="small" type="danger" @click="removeImage">
                  <i class="btn-icon">🗑️</i>
                  删除
                </el-button>
              </div>
            </div>

            <!-- Folder Preview -->
            <div v-if="selectedInputType === 'folder' && previewsrclist.length > 0" class="full-preview-container">
              <div class="preview-image-wrapper" @click="openImagePreview">
                <img :src="previewsrclist[currentImageIndex]" alt="Folder Image" class="full-preview-image"
                  title="点击放大查看">
                <div class="image-overlay">
                  <div class="overlay-content">
                    <i class="zoom-icon">🔍</i>
                    <span>点击放大</span>
                  </div>
                </div>
              </div>
              <div class="folder-navigation-bar">
                <div class="nav-controls">
                  <el-button size="small" @click="previousImage" :disabled="currentImageIndex === 0">
                    <i class="btn-icon">⬅️</i>
                    上一张
                  </el-button>
                  <div class="folder-info">
                    <span class="image-counter">{{ currentImageIndex + 1 }} / {{ previewsrclist.length }}</span>
                    <span class="folder-name">文件夹图像</span>
                    <!-- <span v-if="getCurrentImageType()" class="image-type" :class="getCurrentImageType()">
                      {{ getCurrentImageType() === 'infrared' ? '红外图像' : '可见光图像' }}
                    </span> -->
                  </div>
                  <el-button size="small" @click="nextImage"
                    :disabled="currentImageIndex === previewsrclist.length - 1">
                    下一张
                    <i class="btn-icon">➡️</i>
                  </el-button>
                </div>
                <el-button size="small" type="danger" @click="removeCurrentImage">
                  <i class="btn-icon">🗑️</i>
                  删除当前图像
                </el-button>
              </div>
            </div>

            <!-- Video Preview -->
            <div v-if="selectedInputType === 'video' && selectedVideo" class="full-preview-container">
              <div class="video-preview-wrapper">
                <video ref="videoElement" :src="getVideoSource()" controls class="full-preview-video" preload="metadata"
                  @loadedmetadata="onVideoLoaded" @error="onVideoError" @canplay="onVideoCanPlay" @play="onVideoPlay"
                  @pause="onVideoPause" @timeupdate="onVideoTimeUpdate" @seeking="onVideoSeeking"
                  @seeked="onVideoSeeked">
                  您的浏览器不支持视频播放
                </video>
                <!-- 隐藏的Canvas用于帧提取 -->
                <canvas ref="frameCanvas" style="display: none;"></canvas>
                <div v-if="videoLoading" class="video-loading">
                  <div class="loading-spinner"></div>
                  <span>视频加载中...</span>
                </div>
                <div v-if="videoError" class="video-error">
                  <i class="error-icon">⚠️</i>
                  <span>视频加载失败，请重新选择</span>
                </div>
                <!-- 实时检测状态指示器 -->
                <div v-if="videoRealTimeDetection.isActive" class="realtime-detection-indicator">
                  <div class="indicator-dot"></div>
                  <span>实时检测中...</span>
                </div>
              </div>
              <div class="video-actions-bar">
                <div class="video-info">
                  <span class="video-name">{{ selectedVideoFile ? selectedVideoFile.name : '视频文件' }}</span>
                  <span v-if="selectedVideoFile" class="video-size">{{ formatFileSize(selectedVideoFile.size) }}</span>
                </div>
                <el-button size="small" type="danger" @click="removeVideo">
                  <i class="btn-icon">🗑️</i>
                  删除
                </el-button>
              </div>
            </div>

            <!-- Real-time Stream -->
            <div v-if="selectedInputType === 'realtime'" class="full-preview-container">
              <div class="video-preview-wrapper">
                <video id="realtimeVideo" autoplay controls class="full-preview-video" muted>
                  您的浏览器不支持视频播放
                </video>
              </div>
              <div class="stream-actions-bar">
                <div class="stream-status">
                  <div class="status-dot active"></div>
                  <span class="status-text">实时流已连接</span>
                </div>
                <el-button size="small" type="danger" @click="disconnectRtsp">
                  <i class="btn-icon">🔌</i>
                  断开连接
                </el-button>
              </div>
            </div>

            <!-- Empty State -->
            <div v-if="!hasInputData" class="empty-state">
              <div class="empty-content">
                <i class="empty-icon">📂</i>
                <h3 class="empty-title">请选择数据源</h3>
                <p class="empty-description">选择输入类型并上传文件开始分析</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 2. Detection Results Area (Top Right) -->
      <div class="section detection-results-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="icon">🎯</i>
            检测结果
          </h3>
          <div class="section-actions">
            <!-- Result Mode Switch -->
            <div class="mode-switch-container">
              <span class="mode-label">显示模式:</span>
              <el-switch v-model="resultDisplayMode" active-value="cumulative" inactive-value="single"
                active-text="累计结果" inactive-text="单次结果" class="mode-switch" @change="onDisplayModeChange" />
            </div>
          </div>
        </div>

        <div class="section-content">
          <!-- Results Table Container -->
          <div class="results-table-container">
            <el-table ref="resultsTable" :data="getCachedPaginatedResults()" class="results-table" row-key="id"
              @selection-change="handleSelectionChange" @select-all="handleSelectAll"
              :header-cell-style="{ background: 'var(--table-header-bg)', color: 'var(--text-primary)', fontWeight: '600' }"
              :cell-style="{ background: 'var(--table-cell-bg)', color: 'var(--text-primary)' }"
              :row-style="{ background: 'var(--table-row-bg)' }" stripe border height="100%" table-layout="fixed"
              :default-sort="{ prop: 'timestamp', order: 'descending' }" :show-overflow-tooltip="false">
              <el-table-column type="selection" width="50" header-align="center" align="center" fixed="left"
                :reserve-selection="true">
                <template #header>
                  <el-checkbox v-model="allResultsSelected" @change="toggleAllSelection"
                    :indeterminate="hasSelectedResults && !allResultsSelected" class="select-all-checkbox" />
                </template>
              </el-table-column>

              <el-table-column label="结果图像" width="200" header-align="center" align="center" fixed="left">
                <template #default="scope">
                  <div class="table-image-container"
                    :class="{ 'disabled': getCachedUniqueCategories(scope.row).length === 0 }"
                    @click="showResultDetails(scope.row)">
                    <img :ref="`result-image-${scope.row.id}`" :src="getImageUrl(scope.row.image)"
                      class="table-result-image" alt="检测结果图像" @load="onImageLoad(scope.row)"
                      @error="onImageError(scope.row)" />
                    <canvas :ref="`result-canvas-${scope.row.id}`" class="table-result-canvas" width="160"
                      height="100"></canvas>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="检测信息" min-width="180" header-align="center">
                <template #default="scope">
                  <div class="table-result-info">
                    <div class="info-stats">
                      <div class="stat-item">
                        <span class="stat-label">种类</span>
                        <span class="stat-value">{{ getCachedUniqueCategories(scope.row).length }}</span>
                      </div>
                      <div class="stat-item">
                        <span class="stat-label">总数</span>
                        <span class="stat-value">{{ scope.row.detections.length }}</span>
                      </div>
                    </div>
                    <div class="info-timestamp">
                      {{ getCachedFormattedTimestamp(scope.row.timestamp) }}
                    </div>
                    <div class="info-categories">
                      <el-tag v-for="category in getCachedUniqueCategories(scope.row)" :key="category" size="small"
                        class="category-tag" effect="dark">
                        {{ category }}
                      </el-tag>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="200" header-align="center" align="center" fixed="right">
                <template #default="scope">
                  <div class="table-actions">
                    <el-button size="small" type="warning" @click="editResult(scope.row)" class="action-btn">
                      修正
                    </el-button>
                    <el-button size="small" type="success" @click="saveResult(scope.row)" class="action-btn">
                      保存
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- Bottom Controls -->
          <div class="results-bottom-controls">
            <!-- Pagination -->
            <div class="pagination-container">
              <el-pagination v-model:current-page="currentResultPage" v-model:page-size="resultsPerPage"
                :page-sizes="[5, 10, 20, 50]" :total="detectionResults.length" layout="total,sizes, prev, pager, next"
                @size-change="handlePageSizeChange" @current-change="handleCurrentPageChange"
                class="result-pagination" />
            </div>

            <!-- Batch Operations -->
            <div class="batch-operations">
              <el-button size="small" type="danger" @click="deleteSelectedResults" :disabled="!hasSelectedResults"
                class="batch-btn">
                删除选中
              </el-button>
              <el-button size="small" type="warning" @click="clearAllResults" class="batch-btn">
                清空全部
              </el-button>
              <el-button size="small" type="success" @click="batchSaveResults" :disabled="!hasSelectedResults"
                class="batch-btn">
                批量保存
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 3. Operations Area (Bottom Left) -->
      <div class="section operations-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="icon">⚙️</i>
            操作控制
          </h3>
          <!-- Operation Controls in Header -->
          <div class="operation-controls">
            <!-- Preprocessing Controls -->
            <div class="operation-group">
              <el-button type="primary" @click="preprocessData" :disabled="!hasInputData || isProcessing"
                class="header-operation-btn" size="small">
                <i class="btn-icon">🔧</i>
                预处理
              </el-button>
              <el-select v-model="selectedPreprocessMethod" class="header-method-select" size="small">
                <el-option label="低光照" value="light"></el-option>
                <el-option label="去噪" value="denoise"></el-option>
                <el-option label="去雾" value="dehaze"></el-option>
                <el-option label="去运动模糊" value="motion"></el-option>
                <el-option label="超分辨" value="superres"></el-option>
              </el-select>
            </div>

            <!-- Detection Controls -->
            <div class="operation-group">
              <!-- 图像检测控件 -->
              <template v-if="selectedInputType !== 'video'">
                <el-button type="success" @click="startDetection" :disabled="!hasInputData || isProcessing"
                  class="header-operation-btn" size="small">
                  <i class="btn-icon">🎯</i>
                  {{ isProcessing ? '检测中...' : '检测识别' }}
                </el-button>
                <el-select v-model="selectedModel" class="header-method-select" style="width: 80px;" size="small">
                  <el-option label="检测识别" value="yolov8"></el-option>
                  
                  <el-option label="小样本" value="sample"></el-option>
                </el-select>
              </template>
              <el-button v-if="selectedModel === 'sample'" type="success" @click="startSmallInit">
                模型预训练
              </el-button>
              <!-- 视频检测控件 -->
              <template v-if="selectedInputType === 'video'">
                <!-- <el-button type="primary" @click="detectCurrentVideoFrame" :disabled="!selectedVideo || isProcessing"
                  class="header-operation-btn" size="small">
                  <i class="btn-icon">📹</i>
                  {{ isProcessing ? '检测中...' : '检测当前帧' }}
                </el-button> -->
                <el-button type="success" @click="toggleVideoRealTimeDetection" :disabled="!selectedVideo"
                  class="header-operation-btn" size="small">
                  <i class="btn-icon">⏯️</i>
                  {{ videoRealTimeDetection.isActive ? '停止实时检测' : '开始实时检测' }}
                </el-button>
              </template>

              <!-- 输入源选择开关 -->
              <el-switch v-model="inputSourceSwitch" :disabled="isSwitchDisabled" class="input-source-switch"
                size="small" active-text="预处理" inactive-text="原文件" active-color="#409EFF" inactive-color="#909399" />
            </div>


          </div>
        </div>
        <div class="section-content">
          <!-- Results Preview Area -->
          <div class="results-preview-area">
            <!-- Processing Status Overlay -->
            <div v-if="isProcessing || (processingProgress === 100 && showProcessingComplete)"
              class="processing-status-overlay">
              <div class="processing-status-card">
                <div class="status-info">
                  <span class="status-title">处理状态: {{ processingStatus }}</span>
                  <span class="status-percentage">{{ processingProgress }}%</span>
                </div>
                <div class="status-progress-bar">
                  <div class="status-progress" :style="{ width: processingProgress + '%' }"></div>
                </div>
              </div>
            </div>

            <!-- Preview Results Grid -->
            <div class="preview-results-grid">
              <!-- Preprocessing Result -->
              <div class="result-container">
                <div class="result-header">
                  <span class="result-title">预处理结果</span>
                  <div class="result-controls">
                    <!-- Navigation for folder type -->
                    <div v-if="selectedInputType === 'folder' && previewsrclist.length > 1" class="nav-controls-small">
                      <el-button size="small" @click="previousPreprocessResult"
                        :disabled="currentPreprocessIndex === 0">
                        <i class="btn-icon">⬅️</i>
                      </el-button>
                      <span class="nav-counter">{{ currentPreprocessIndex + 1 }}/{{ previewsrclist.length }}</span>
                      <el-button size="small" @click="nextPreprocessResult"
                        :disabled="currentPreprocessIndex === previewsrclist.length - 1">
                        <i class="btn-icon">➡️</i>
                      </el-button>
                    </div>
                    <el-button size="small" @click="confirmPreprocessResults" :disabled="!hasPreprocessResults"
                      type="success">
                      确认
                    </el-button>
                    <el-button size="small" @click="showPreprocessDetails">
                      详情
                    </el-button>
                  </div>
                </div>
                <div class="result-wrapper">
                  <!-- Image/Folder Preprocessing Result -->
                  <div v-if="selectedInputType === 'image' || selectedInputType === 'folder'"
                    class="image-result-container">
                    <img :src="getCurrentPreprocessImage()" alt="Preprocessing Result" class="result-image"
                      @click="showPreprocessDetails">
                  </div>
                  <!-- Video Preprocessing Result -->
                  <div v-else-if="selectedInputType === 'video'" class="video-result-container">
                    <video v-if="selectedVideo" :src="preprocessedVideoUrl()" class="result-video" controls
                      @click="showPreprocessDetails">
                      预处理视频不可用
                    </video>
                  </div>
                  <!-- Real-time or fallback -->
                  <div v-else class="canvas-result-container">
                    <canvas ref="preprocessCanvas" class="result-canvas" width="300" height="200"
                      @click="showPreprocessDetails"></canvas>
                  </div>
                </div>
              </div>
            </div>

            <!-- Real-time Status (only for real-time video) -->
            <div v-if="selectedInputType === 'realtime'" class="realtime-status-panel">
              <div class="status-indicator">
                <div class="status-dot" :class="{ active: isRealTimeDetecting }"></div>
                <span class="status-text">{{ isRealTimeDetecting ? '实时处理中' : '待机状态' }}</span>
              </div>
              <div class="realtime-stats">
                <div class="stat-item">
                  <span class="stat-label">FPS:</span>
                  <span class="stat-value">{{ realtimeFps }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">延迟:</span>
                  <span class="stat-value">{{ realtimeLatency }}ms</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 4. Results Statistics/Map Area (Bottom Right) - Only show for realtime -->
      <div v-if="selectedInputType === 'realtime1'" class="section statistics-map-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="icon">🗺️</i>
            实时地图
          </h3>
          <div class="section-actions">
            <button @click="clearScreen" class="action-btn">清屏</button>
            <button @click="toggleMarkerLock" class="action-btn">
              {{ markersLocked ? '解锁标记' : '锁定标记' }}
            </button>
            <button @click="clearFlightPath" class="action-btn">清除航迹</button>
          </div>
        </div>
        <div class="section-content">
          <!-- Real-time Map (only for real-time video) -->
          <div class="map-container">
            <MapComponent :gps-coordinates="currentGpsCoordinates" :markers-locked="markersLocked"
              :flight-path="flightPath" @marker-click="handleMarkerClick" ref="mapComponent" />
          </div>
        </div>
      </div>
    </div>

    <!-- Image Preview Modal -->
    <el-dialog v-model="imagePreview.show" :title="getPreviewTitle()" width="90%" :before-close="closeImagePreview"
      class="image-preview-modal" top="5vh">
      <div class="modal-image-container">
        <!-- Image with zoom controls -->
        <div class="image-zoom-container" ref="imageZoomContainer">
          <img :src="imagePreview.currentImage" alt="Preview Image" class="modal-preview-image" :style="imageZoomStyle"
            @wheel="handleImageWheel" @mousedown="startImageDrag" @dragstart.prevent ref="previewImage">
        </div>

        <!-- Zoom Controls -->
        <div class="zoom-controls">
          <el-button size="small" @click="zoomIn" :disabled="imagePreview.zoom >= 5">
            <i class="btn-icon">🔍+</i>
          </el-button>
          <span class="zoom-level">{{ Math.round(imagePreview.zoom * 100) }}%</span>
          <el-button size="small" @click="zoomOut" :disabled="imagePreview.zoom <= 0.1">
            <i class="btn-icon">🔍-</i>
          </el-button>
          <el-button size="small" @click="resetZoom">
            <i class="btn-icon">🔄</i>
            重置
          </el-button>
        </div>

        <!-- Navigation for folder images -->
        <div v-if="selectedInputType === 'folder' && previewsrclist.length > 1" class="modal-navigation">
          <el-button size="small" @click="previousPreviewImage" :disabled="imagePreview.currentIndex === 0"
            class="nav-btn nav-prev">
            <i class="btn-icon">⬅️</i>
            上一张
          </el-button>

          <div class="image-info">
            <span class="image-counter">{{ imagePreview.currentIndex + 1 }} / {{ previewsrclist.length }}</span>
            <div class="image-name">{{ getImageName(imagePreview.currentIndex) }}</div>
          </div>

          <el-button size="small" @click="nextPreviewImage"
            :disabled="imagePreview.currentIndex === previewsrclist.length - 1" class="nav-btn nav-next">
            下一张
            <i class="btn-icon">➡️</i>
          </el-button>
        </div>
      </div>

      <template #footer>
        <div class="modal-footer">
          <div class="footer-left">
            <el-button size="small" @click="rotateImage(-90)">
              <i class="btn-icon">↺</i>
              左转
            </el-button>
            <el-button size="small" @click="rotateImage(90)">
              <i class="btn-icon">↻</i>
              右转
            </el-button>
          </div>
          <div class="footer-right">
            <el-button @click="closeImagePreview">关闭</el-button>
            <el-button type="primary" @click="downloadCurrentImage">下载图像</el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- Preprocessing Details Dialog -->
    <el-dialog v-model="showPreprocessDetailsDialog" :title="getPreprocessDetailsTitle()" width="90%" top="5vh"
      class="details-dialog" @close="closePreprocessDetails">
      <div class="details-content">
        <!-- Image/Folder Details -->
        <div v-if="selectedInputType === 'image' || selectedInputType === 'folder'" class="image-details-container">
          <div class="comparison-view">
            <div class="original-view">
              <h4 class="view-title">原图</h4>
              <div class="image-viewer" ref="originalImageViewer">
                <img :src="getOriginalImageForDetails()" alt="Original Image" class="detail-image"
                  :style="originalImageStyle" @wheel="handleOriginalImageWheel" @mousedown="startOriginalImageDrag"
                  ref="originalDetailImage">
              </div>
            </div>
            <div class="processed-view">
              <h4 class="view-title">预处理结果</h4>
              <div class="image-viewer" ref="processedImageViewer">
                <img :src="getCurrentPreprocessImage()" alt="Processed Image" class="detail-image"
                  :style="processedImageStyle" @wheel="handleProcessedImageWheel" @mousedown="startProcessedImageDrag"
                  ref="processedDetailImage">
              </div>
            </div>
          </div>

          <!-- Image Controls -->
          <div class="details-controls">
            <div class="zoom-controls">
              <el-button size="small" @click="syncZoomIn">放大</el-button>
              <el-button size="small" @click="syncZoomOut">缩小</el-button>
              <el-button size="small" @click="resetDetailsZoom">重置</el-button>
              <el-button size="small" @click="fitImagesToContainer" type="primary">适应窗口</el-button>
              <span class="zoom-info">{{ Math.round(detailsZoom * 100) }}%</span>
            </div>

            <!-- Navigation for folder -->
            <div v-if="selectedInputType === 'folder'" class="details-navigation">
              <el-button size="small" @click="previousDetailsImage" :disabled="currentPreprocessIndex === 0">
                上一张
              </el-button>
              <span class="details-counter">{{ currentPreprocessIndex + 1 }} / {{ previewsrclist.length }}</span>
              <el-button size="small" @click="nextDetailsImage"
                :disabled="currentPreprocessIndex === previewsrclist.length - 1">
                下一张
              </el-button>
            </div>
          </div>
        </div>

        <!-- Video Details -->
        <div v-else-if="selectedInputType === 'video'" class="video-details-container">
          <div class="video-comparison-view">
            <div class="original-video-view">
              <h4 class="view-title">原视频</h4>
              <video :src="selectedVideo" ref="originalDetailVideo" class="detail-video" controls>
                原视频不可用
              </video>
            </div>
            <div class="processed-video-view">
              <h4 class="view-title">预处理结果</h4>
              <video :src="preprocessedVideoUrl()" ref="processedDetailVideo" class="detail-video" controls>
                预处理视频不可用
              </video>
            </div>
          </div>

          <!-- Video Controls -->
          <div class="video-details-controls">
            <el-button size="small" @click="syncVideoPlay">同步播放</el-button>
            <el-button size="small" @click="syncVideoPause">同步暂停</el-button>
            <el-button size="small" @click="frameByFrame(-1)">上一帧</el-button>
            <el-button size="small" @click="frameByFrame(1)">下一帧</el-button>
            <el-button size="small" @click="switchMasterVideo" type="info">
              主视频: {{ videoSyncState.masterVideo === 'original' ? '原视频' : '处理后' }}
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- Result Details Dialog -->
    <el-dialog v-model="showResultDetailsDialog" :title="getResultDetailsTitle()" width="95%" top="2vh"
      class="details-dialog result-details-dialog" @close="closeResultDetails" :close-on-click-modal="false">
      <div class="result-details-content">
        <!-- Left Side: Image with Annotations -->
        <div class="result-details-left">
          <div class="details-image-container">
            <!-- Image Header with Controls -->
            <div class="details-image-header">
              <div class="header-left">
                <h4 class="details-section-title">
                  <i class="icon">🖼️</i>
                  检测图像
                </h4>
                <div class="zoom-info">
                  缩放: {{ Math.round(resultDetailsZoom * 100) }}%
                </div>
              </div>
              <div class="header-controls">
                <!-- <el-button size="small" @click="toggleResultAnnotations" :type="showAnnotations ? 'primary' : 'default'"
                  class="annotation-toggle-btn">
                  <i class="el-icon-view"></i>
                  {{ showAnnotations ? '隐藏标注' : '显示标注' }}
                </el-button> -->
              </div>
            </div>

            <!-- Image Viewer with Enhanced Interaction -->
            <div class="details-image-viewer" ref="resultDetailsImageViewer">
              <div class="image-viewer-wrapper">
                <img ref="resultDetailsImage"
                  :src="selectedResultForDetails ? getImageUrl(selectedResultForDetails.image) : ''"
                  class="details-image" alt="检测结果详情图像" :style="resultDetailsImageStyle"
                  @wheel="handleResultDetailsWheel" @mousedown="startResultDetailsDrag" @load="onDetailsImageLoad"
                  @error="onDetailsImageError" />

                <!-- Drag Hint -->
                <div v-if="resultDetailsZoom > 1 && !resultDetailsDragging" class="drag-hint">
                  <i class="el-icon-rank"></i>
                  拖拽移动图像
                </div>
              </div>
            </div>

            <!-- Navigation Controls -->
            <div v-if="detectionResults.length > 1" class="details-navigation">
              <div class="nav-controls">
                <el-button size="small" @click="previousResultDetails" :disabled="getCurrentResultIndex() === 0"
                  class="nav-btn">
                  <i class="el-icon-arrow-left"></i>
                  上一张
                </el-button>
                <div class="nav-counter">
                  <span class="current-index">{{ getCurrentResultIndex() + 1 }}</span>
                  <span class="separator">/</span>
                  <span class="total-count">{{ detectionResults.length }}</span>
                </div>
                <el-button size="small" @click="nextResultDetails"
                  :disabled="getCurrentResultIndex() === detectionResults.length - 1" class="nav-btn">
                  下一张
                  <i class="el-icon-arrow-right"></i>
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Side: Detection Information -->
        <div class="result-details-right">
          <div class="details-info-container">
            <div class="details-info-header">
              <h4 class="details-section-title">检测信息</h4>
            </div>

            <!-- Summary Statistics -->
            <div class="detection-summary">
              <div class="summary-card">
                <div class="summary-label">检测种类总数</div>
                <div class="summary-value">{{ getUniqueCategories(selectedResultForDetails?.detections || []).length }}
                </div>
              </div>
              <div class="summary-card">
                <div class="summary-label">检测目标总数</div>
                <div class="summary-value">{{ selectedResultForDetails?.detections?.length || 0 }}</div>
              </div>
            </div>

            <!-- Category Statistics -->
            <div class="category-statistics">
              <h5 class="subsection-title">种类统计</h5>
              <div class="category-list">
                <div v-for="(count, category) in getCategoryStatistics(selectedResultForDetails?.detections || [])"
                  :key="category" class="category-item">
                  <span class="category-name">{{ category }}</span>
                  <span class="category-count">{{ count }}</span>
                </div>
              </div>
            </div>

            <!-- Detailed Detection List -->
            <div class="detection-details-list">
              <h5 class="subsection-title">
                <i class="icon">📋</i>
                详细信息
              </h5>
              <div class="details-table-container">
                <el-table :data="selectedResultForDetails?.detections || []" class="detection-table" size="small"
                  max-height="350" :header-cell-style="{
                    background: 'rgba(30, 35, 45, 0.9)',
                    color: '#ffffff',
                    fontWeight: '600',
                    fontSize: '12px',
                    padding: '12px 8px'
                  }" :cell-style="{
                    background: 'rgba(20, 25, 35, 0.8)',
                    color: '#e8e8e8',
                    padding: '10px 8px',
                    fontSize: '12px'
                  }" :row-style="{ background: 'rgba(25, 30, 40, 0.85)' }" stripe border>
                  <el-table-column prop="category" label="类别" width="90" align="center">
                    <template #default="scope">
                      <el-tag :type="getCategoryTagType(scope.row.category)" size="small" class="category-tag-small"
                        effect="dark">
                        {{ scope.row.category }}
                      </el-tag>
                    </template>
                  </el-table-column>

                  <el-table-column label="位置信息" min-width="160">
                    <template #default="scope">
                      <div class="bbox-info">
                        <div class="bbox-row">
                          <span class="bbox-label">X:</span>
                          <span class="bbox-value">{{ getPixelCoords(scope.row.boundingBox).x }}</span>
                          <span class="bbox-label">Y:</span>
                          <span class="bbox-value">{{ getPixelCoords(scope.row.boundingBox).y }}</span>
                        </div>
                        <div class="bbox-row">
                          <span class="bbox-label">W:</span>
                          <span class="bbox-value">{{ getPixelCoords(scope.row.boundingBox).width }}</span>
                          <span class="bbox-label">H:</span>
                          <span class="bbox-value">{{ getPixelCoords(scope.row.boundingBox).height }}</span>
                        </div>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="置信度" width="100" align="center">
                    <template #default="scope">
                      <div class="confidence-display">
                        <div class="confidence-bar">
                          <div class="confidence-fill" :style="{ width: scope.row.confidence + '%' }"></div>
                        </div>
                        <span class="confidence-text">{{ scope.row.confidence.toFixed(1) }}%</span>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- Detection Details Dialog -->
    <el-dialog v-model="showDetectionDetailsDialog" title="检测结果详情" width="90%" top="5vh" class="details-dialog"
      @close="closeDetectionDetails" :close-on-click-modal="false" :close-on-press-escape="false" :resize="false"
      :draggable="false" :lock-scroll="true">
      <div class="details-content">
        <div class="detection-details-container">
          <!-- Left Side: Canvas Area -->
          <div class="detection-details-left">
            <div class="detection-comparison-view">
              <div class="overlapped-canvas-container">
                <h4 class="view-title">检测结果详情</h4>
                <div class="canvas-viewer-stack" ref="canvasViewerStack">
                  <div class="canvas-layer original-layer">
                    <!-- <canvas ref="originalDetectionCanvas" class="detail-canvas original-canvas" width="400"
                      height="300"></canvas> -->
                  </div>
                  <div class="canvas-layer result-layer">
                    <canvas ref="resultDetectionCanvas" class="detail-canvas result-canvas" width="400" height="300"
                      @click="zoomDetectionCanvas"></canvas>
                  </div>
                </div>
              </div>
            </div>

            <!-- Detection Controls -->
            <div class="detection-details-controls">
              <div class="detection-options">
                <el-button size="small" @click="resetDetectionZoom">重置视图</el-button>
              </div>

              <!-- Navigation for folder -->
              <div v-if="selectedInputType === 'folder'" class="details-navigation">
                <el-button size="small" @click="previousDetectionDetails" :disabled="currentDetectionIndex === 0">
                  上一张
                </el-button>
                <span class="details-counter">{{ currentDetectionIndex + 1 }} / {{ previewsrclist.length }}</span>
                <el-button size="small" @click="nextDetectionDetails"
                  :disabled="currentDetectionIndex === previewsrclist.length - 1">
                  下一张
                </el-button>
              </div>
            </div>
          </div>

          <!-- Right Side: Detection Information -->
          <div class="detection-details-right">
            <div class="detection-info-container">
              <div class="detection-info-header">
                <h4 class="details-section-title">检测信息</h4>
              </div>

              <!-- Summary Statistics -->
              <div class="detection-summary">
                <div class="summary-card">
                  <div class="summary-label">检测种类总数</div>
                  <div class="summary-value">{{ selectedResultForDetails ?
                    getUniqueCategories(selectedResultForDetails.detections || []).length : 0 }}</div>
                </div>
                <div class="summary-card">
                  <div class="summary-label">检测目标总数</div>
                  <div class="summary-value">{{ selectedResultForDetails ? (selectedResultForDetails.detections?.length
                    ||
                    0) : 0 }}</div>
                </div>
              </div>

              <!-- Detection Status -->
              <div class="detection-status">
                <div class="status-indicator"
                  :class="{ 'has-detections': selectedResultForDetails?.has_detections || false }">
                  <i class="status-icon"
                    :class="(selectedResultForDetails?.has_detections || false) ? 'el-icon-success' : 'el-icon-info'"></i>
                  <span class="status-text">
                    {{ selectedResultForDetails ? (selectedResultForDetails.has_detections ? '检测到目标' : '未检测到目标') :
                      '等待检测' }}
                  </span>
                </div>
              </div>

              <!-- Category Statistics -->
              <div v-if="selectedResultForDetails?.has_detections" class="category-statistics">
                <h5 class="subsection-title">种类统计</h5>
                <div class="category-list">
                  <div v-for="(count, category) in getCategoryStatistics(selectedResultForDetails?.detections || [])"
                    :key="category" class="category-item">
                    <span class="category-name">{{ category }}</span>
                    <span class="category-count">{{ count }}</span>
                  </div>
                </div>
              </div>

              <!-- Default Content when no data -->
              <div v-if="!selectedResultForDetails" class="detection-placeholder">
                <div class="placeholder-content">
                  <i class="placeholder-icon el-icon-picture-outline"></i>
                  <p class="placeholder-text">请选择检测结果查看详情</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- Enhanced RTSP Configuration Modal -->
    <el-dialog v-model="showRtspModal" title="RTSP 流配置" width="600px" class="rtsp-config-dialog"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="rtsp-config-content">
        <!-- Connection Status -->
        <div class="connection-status-card">
          <div class="status-header">
            <div class="status-indicator">
              <div class="status-dot" :class="{
                'connecting': rtspConnectionStatus === 'connecting',
                'connected': rtspConnectionStatus === 'connected',
                'error': rtspConnectionStatus === 'error',
                'disconnected': rtspConnectionStatus === 'disconnected'
              }"></div>
              <span class="status-text">{{ getConnectionStatusText() }}</span>
            </div>
            <div v-if="rtspConnectionStatus === 'connected'" class="connection-info">
              <span class="info-item">
                <i class="el-icon-time"></i>
                已连接 {{ formatConnectionTime() }}
              </span>
            </div>
          </div>
        </div>

        <!-- Configuration Form -->
        <div class="config-form">
          <el-tabs v-model="activeConfigTab" class="config-tabs">
            <!-- Quick Connect Tab -->
            <el-tab-pane label="快速连接" name="quick">
              <div class="quick-connect-form">
                <div class="form-section">
                  <label class="form-label">
                    <i class="el-icon-link"></i>
                    RTSP 地址
                  </label>
                  <el-input v-model="rtspConfig.url" placeholder="***********:8554/live/wideangle"
                    class="rtsp-url-input" :disabled="rtspConnectionStatus === 'connecting'">
                    <template #prepend>rtsp://</template>
                  </el-input>
                  <div class="input-hint">
                    支持格式：rtsp://ip:port/path 或 rtsp://username:password@ip:port/path
                  </div>
                </div>

                <!-- Preset Configurations -->
                <div class="preset-section">
                  <label class="form-label">
                    <i class="el-icon-collection"></i>
                    预设配置
                  </label>
                  <div class="preset-buttons">
                    <el-button v-for="preset in rtspPresets" :key="preset.name" size="small"
                      @click="applyPreset(preset)" class="preset-btn">
                      {{ preset.name }}
                    </el-button>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- Advanced Settings Tab -->
            <el-tab-pane label="高级设置" name="advanced">
              <div class="advanced-form">
                <el-row :gutter="16">
                  <el-col :span="12">
                    <div class="form-section">
                      <label class="form-label">服务器地址</label>
                      <el-input v-model="rtspConfig.host" placeholder="*************" />
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="form-section">
                      <label class="form-label">端口</label>
                      <el-input-number v-model="rtspConfig.port" :min="1" :max="65535" placeholder="554" />
                    </div>
                  </el-col>
                </el-row>

                <el-row :gutter="16">
                  <el-col :span="12">
                    <div class="form-section">
                      <label class="form-label">用户名</label>
                      <el-input v-model="rtspConfig.username" placeholder="admin" />
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="form-section">
                      <label class="form-label">密码</label>
                      <el-input v-model="rtspConfig.password" type="password" placeholder="password" show-password />
                    </div>
                  </el-col>
                </el-row>

                <div class="form-section">
                  <label class="form-label">流路径</label>
                  <el-input v-model="rtspConfig.path" placeholder="/stream" />
                </div>

                <div class="form-section">
                  <label class="form-label">连接选项</label>
                  <div class="options-grid">
                    <el-checkbox v-model="rtspConfig.options.tcp">使用 TCP 传输</el-checkbox>
                    <el-checkbox v-model="rtspConfig.options.autoReconnect">自动重连</el-checkbox>
                    <el-checkbox v-model="rtspConfig.options.lowLatency">低延迟模式</el-checkbox>
                    <el-checkbox v-model="rtspConfig.options.hardwareAccel">硬件加速</el-checkbox>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- Connection Test -->
        <div class="connection-test">
          <el-button @click="testConnection" :loading="rtspConnectionStatus === 'connecting'"
            :disabled="!canTestConnection" class="test-btn">
            <i class="el-icon-connection"></i>
            测试连接
          </el-button>
          <div v-if="connectionTestResult" class="test-result" :class="connectionTestResult.type">
            <i :class="connectionTestResult.type === 'success' ? 'el-icon-success' : 'el-icon-error'"></i>
            {{ connectionTestResult.message }}
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showRtspModal = false">取消</el-button>
          <el-button v-if="rtspConnectionStatus === 'connected'" type="danger" @click="disconnectRtsp">
            断开连接
          </el-button>
          <el-button type="primary" @click="connectRtsp" :loading="rtspConnectionStatus === 'connecting'"
            :disabled="!canConnect">
            {{ rtspConnectionStatus === 'connected' ? '重新连接' : '连接' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- Modify Detection Results Dialog -->
    <el-dialog v-model="showModifyDialog" title="修正检测结果" width="95%" top="2vh" class="modify-dialog"
      @close="closeModifyDialog" :close-on-click-modal="false">
      <div class="modify-content">
        <!-- Left Side: Image with Editable Annotations -->
        <div class="modify-left">
          <div class="modify-image-container">
            <div class="modify-image-header">
              <h4 class="modify-section-title">
                <i class="icon">✏️</i>
                编辑检测框
              </h4>
              <div class="modify-tools">
                <el-button size="small" @click="addNewDetection" type="primary" class="tool-btn">
                  <i class="el-icon-plus"></i>
                  添加检测框
                </el-button>
                <el-button size="small" @click="deleteSelectedDetection" :disabled="!selectedDetection" type="danger"
                  class="tool-btn">
                  <i class="el-icon-delete"></i>
                  删除选中
                </el-button>
              </div>
            </div>

            <!-- Interactive Canvas -->
            <div class="modify-canvas-container" ref="modifyCanvasContainer">
              <canvas ref="modifyCanvas" class="modify-canvas" @mousedown="onCanvasMouseDown"
                @mousemove="onCanvasMouseMove" @mouseup="onCanvasMouseUp" @click="onCanvasClick"></canvas>
            </div>

            <!-- Instructions -->
            <div class="modify-instructions">
              <p><strong>操作说明：</strong></p>
              <ul>
                <li>点击检测框选中，拖拽移动位置</li>
                <li>点击"添加检测框"后在图像上拖拽创建新框</li>
                <li>右侧可以修改检测框的类别和置信度</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Right Side: Detection List and Properties -->
        <div class="modify-right">
          <div class="modify-detections-container">
            <div class="modify-detections-header">
              <h4 class="modify-section-title">检测列表</h4>
              <span class="detection-count">共 {{ modifyDetections.length }} 个</span>
            </div>

            <!-- Detection List -->
            <div class="modify-detections-list">
              <div v-for="(detection, index) in modifyDetections" :key="index" class="detection-item"
                :class="{ selected: selectedDetection === detection }" @click="selectDetection(detection)">
                <div class="detection-info">
                  <div class="detection-category">
                    <el-select v-model="detection.category" size="small" class="category-select">
                      <el-option v-for="category in availableCategories" :key="category" :label="category"
                        :value="category"></el-option>
                    </el-select>
                  </div>
                  <div class="detection-confidence">
                    <span class="confidence-label">置信度:</span>
                    <el-input-number v-model="detection.confidence" :min="0" :max="100" :step="0.1" size="small"
                      class="confidence-input"></el-input-number>
                    <span class="confidence-unit">%</span>
                  </div>
                </div>
                <div class="detection-actions">
                  <el-button size="small" type="danger" @click="removeDetection(index)">
                    删除
                  </el-button>
                </div>
              </div>
            </div>

            <!-- Add New Category -->
            <div class="add-category-section">
              <h5>添加新类别</h5>
              <div class="add-category-form">
                <el-input v-model="newCategoryName" placeholder="输入新类别名称" size="small" class="category-input"
                  @keyup.enter="addNewCategory"></el-input>
                <el-button size="small" type="primary" @click="addNewCategory">添加</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="modify-footer">
          <el-button @click="closeModifyDialog">取消</el-button>
          <el-button type="primary" @click="saveModifiedResult">保存修改</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import HeaderComponent from '@/components/header.vue'
import MapComponent from '@/components/map.vue'
import { ElSelect, ElOption, ElButton, ElDialog } from 'element-plus'
import { APIDetectFloder, APIUploadData, APIPreprocessData, APIDetectData, APIGetCategoryTree, APIAddSamples, APISaveCategoryTree, APIDetectVideoTimestamp, APIInitSmall} from '@/api/api'
import { serverAddress } from '@/api/config'
import MediaManager from '@/utils/MediaManager.js'

// 事件管理器类 - 统一管理全局事件监听器
class EventManager {
  constructor() {
    this.listeners = new Map()
    this.timers = new Set()
    this.animationFrames = new Set()
  }

  // 添加事件监听器
  addEventListener(target, event, handler, options = {}) {
    const key = `${target.constructor.name}-${event}-${handler.name}`

    // 如果已存在相同的监听器，先移除
    if (this.listeners.has(key)) {
      this.removeEventListener(key)
    }

    target.addEventListener(event, handler, options)
    this.listeners.set(key, { target, event, handler, options })

    return key
  }

  // 移除特定事件监听器
  removeEventListener(key) {
    const listener = this.listeners.get(key)
    if (listener) {
      listener.target.removeEventListener(listener.event, listener.handler, listener.options)
      this.listeners.delete(key)
    }
  }

  // 添加定时器管理
  setTimeout(callback, delay) {
    const timerId = setTimeout(() => {
      callback()
      this.timers.delete(timerId)
    }, delay)
    this.timers.add(timerId)
    return timerId
  }

  setInterval(callback, interval) {
    const timerId = setInterval(callback, interval)
    this.timers.add(timerId)
    return timerId
  }

  clearTimer(timerId) {
    if (this.timers.has(timerId)) {
      clearTimeout(timerId)
      clearInterval(timerId)
      this.timers.delete(timerId)
    }
  }

  // 添加动画帧管理
  requestAnimationFrame(callback) {
    const frameId = requestAnimationFrame(() => {
      callback()
      this.animationFrames.delete(frameId)
    })
    this.animationFrames.add(frameId)
    return frameId
  }

  cancelAnimationFrame(frameId) {
    if (this.animationFrames.has(frameId)) {
      cancelAnimationFrame(frameId)
      this.animationFrames.delete(frameId)
    }
  }

  // 清理所有事件监听器、定时器和动画帧
  cleanup() {
    // 清理事件监听器
    for (const [, listener] of this.listeners) {
      listener.target.removeEventListener(listener.event, listener.handler, listener.options)
    }
    this.listeners.clear()

    // 清理定时器
    for (const timerId of this.timers) {
      clearTimeout(timerId)
      clearInterval(timerId)
    }
    this.timers.clear()

    // 清理动画帧
    for (const frameId of this.animationFrames) {
      cancelAnimationFrame(frameId)
    }
    this.animationFrames.clear()
  }

  // 获取当前管理的资源数量（用于调试）
  getStats() {
    return {
      listeners: this.listeners.size,
      timers: this.timers.size,
      animationFrames: this.animationFrames.size
    }
  }
}

export default {
  name: 'DetectedView',
  components: {
    HeaderComponent,
    MapComponent,
    ElSelect,
    ElOption,
    ElButton,
    ElDialog
  },
  data() {
    return {
      smallModelInit: false,
      // 事件管理器实例
      eventManager: null,
      // 媒体资源管理器实例
      mediaManager: null,

      // Input type management
      selectedInputType: 'image',
      inputTypes: [
        { value: 'image', label: '上传图像', icon: '🖼️' },
        { value: 'folder', label: '上传文件夹', icon: '📁' },
        { value: 'video', label: '上传视频', icon: '🎥' },
        { value: 'realtime', label: '实时视频', icon: '📡' }
      ],

      // Data input states
      selectedImage: null,
      selectedImageFile: null,
      selectedVideo: null,
      selectedVideoFile: null,
      previewsrclist: [],
      currentImageIndex: 0,

      // 文件上传状态
      isUploading: false,
      uploadedFilePath: null,
      uploadedFilePaths: [],

      // 预处理结果状态
      preprocessedFilePath: null,
      preprocessedFilePaths: [],

      // 文件状态管理（新增）
      fileManager: {
        uploadedFiles: [], // 上传的文件信息列表
        preprocessedFiles: [], // 预处理的文件信息列表
        currentFileUuid: null, // 当前选中的文件UUID
        inputSource: 'preprocessed' // 检测输入源：'original' 或 'preprocessed'
      },

      // 视频实时检测状态
      videoRealTimeDetection: {
        websocket: null, // WebSocket实例
        isActive: false, // 是否正在进行实时检测
        intervalId: null, // 定时器ID
        frameInterval: 1000, // 检测间隔（毫秒）- 简化为1s
        lastTimestamp: 0, // 上次检测的时间戳
        detectionResults: [], // 实时检测结果列表
        totalDetections: 0, // 总检测数量
        isVideoDragging: false, // 是否正在拖拽视频
        isVideoPaused: false // 视频是否暂停
      },

      webRtcServer: null,
      rtspUrl: null,
      rtspInputUrl: '',
      showRtspModal: false,
      rtspConnectionStatus: 'disconnected', // disconnected, connecting, connected, error
      rtspConnectionTime: null,
      activeConfigTab: 'quick',
      connectionTestResult: null,

      // Enhanced RTSP Configuration
      rtspConfig: {
        url: '',
        host: '',
        port: 554,
        username: '',
        password: '',
        path: '/stream',
        options: {
          tcp: false,
          autoReconnect: true,
          lowLatency: false,
          hardwareAccel: false
        }
      },

      // RTSP Presets
      rtspPresets: [
        {
          name: '海康威视',
          template: 'rtsp://{username}:{password}@{host}:{port}/Streaming/Channels/101'
        },
        {
          name: '大华',
          template: 'rtsp://{username}:{password}@{host}:{port}/cam/realmonitor?channel=1&subtype=0'
        },
        {
          name: '宇视',
          template: 'rtsp://{username}:{password}@{host}:{port}/video1s1'
        },
        {
          name: '通用',
          template: 'rtsp://{username}:{password}@{host}:{port}/stream'
        }
      ],

      // 统一的图像预览状态管理
      imagePreview: {
        show: false,
        currentImage: null,
        currentIndex: 0,
        zoom: 1,
        rotation: 0,
        panX: 0,
        panY: 0,
        isDragging: false,
        dragStartX: 0,
        dragStartY: 0
      },

      // Video states
      videoLoading: false,
      videoError: false,

      // 视频同步状态管理
      videoSyncState: {
        isSyncing: false,           // 防止重复操作
        masterVideo: 'original'     // 主视频标识 ('original' | 'processed')
      },

      // Detection results
      detectionResults: [

      ],
      showAnnotations: true,

      // Detection results UI states
      resultDisplayMode: 'single', // 'single' or 'cumulative'
      currentResultPage: 1,
      resultsPerPage: 10,
      allResultsSelected: false,
      showResultDetailsDialog: false,
      selectedResultForDetails: null,
      resultDetailsZoom: 1,
      resultDetailsPanX: 0,
      resultDetailsPanY: 0,
      resultDetailsDragging: false,
      resultDetailsDragStartX: 0,
      resultDetailsDragStartY: 0,
      resultDetailsDragStartPanX: 0,
      resultDetailsDragStartPanY: 0,
      canvasRenderFrame: null,
      paginationCache: null,
      categoriesCache: null,
      timestampCache: null,

      // Operations control
      isProcessing: false,
      processingProgress: 0,
      processingStatus: '就绪',
      showProcessingComplete: false,
      selectedModel: 'yolov8',
      selectedPreprocessMethod: 'light',
      confidenceThreshold: 75,
      showDetectionBoxes: true,
      preprocessOptions: {
        resize: true,
        normalize: true,
        denoise: false
      },

      // Result navigation indices
      currentPreprocessIndex: 0,
      currentDetectionIndex: 0,

      // Processed results data

      preprocessedImages: [], // For folder type preprocessing results

      // Details dialog states
      showPreprocessDetailsDialog: false,
      showDetectionDetailsDialog: false,
      showDetailsDetectionBoxes: true,

      // Details dialog zoom and pan states
      detailsZoom: 1,
      originalImageZoom: 1,
      processedImageZoom: 1,
      originalImagePanX: 0,
      originalImagePanY: 0,
      processedImagePanX: 0,
      processedImagePanY: 0,
      detailsImageDragging: false,
      dragStartPanX: 0,
      dragStartPanY: 0,

      // Real-time specific
      isRealTimeDetecting: false,
      realtimeMode: 'detection',
      realtimeFps: 30,
      realtimeLatency: 50,
      realtimeOptions: {
        saveFrames: false,
        trackObjects: true
      },

      // Map and GPS data
      currentGpsCoordinates: { lat: 39.9042, lng: 116.4074 },
      markersLocked: false,
      flightPath: [],

      // Sample management data
      categoryTree: [],
      selectedCategory: null,
      showCategoryDialog: false,
      savingResults: false,

      // Manual correction dialog data
      showModifyDialog: false,
      modifyingResult: null,
      modifyCanvas: null,
      modifyContext: null,
      modifyImage: null,
      modifyDetections: [],
      selectedDetection: null,
      isDragging: false,
      isCreatingBox: false,
      dragStartX: 0,
      dragStartY: 0,
      newBoxStart: { x: 0, y: 0 },
      newBoxEnd: { x: 0, y: 0 },
      newCategoryName: '',
      availableCategories: [
        '武装人员', '运输车', '突击车', '82速射迫击炮', '轮式突击炮',
        '防坦克三角锥', '坦克', '地堡', '步兵战车', '榴弹炮', '伪装网', '无人装备'
      ],
    }
  },
  computed: {
    hasInputData() {
      return this.selectedImage || this.selectedVideo || this.previewsrclist.length > 0 || this.rtspUrl
    },

    // 检查是否有预处理结果
    hasPreprocessResults() {
      return this.fileManager.preprocessedFiles.length > 0 ||
        this.preprocessedFilePath ||
        this.preprocessedFilePaths.length > 0
    },

    // 输入源开关的计算属性
    inputSourceSwitch: {
      get() {
        return this.fileManager.inputSource === 'preprocessed'
      },
      set(value) {
        this.fileManager.inputSource = value ? 'preprocessed' : 'original'
        console.log(`输入源切换: ${this.fileManager.inputSource}`)

        // 如果是视频类型，输出当前视频源信息
        if (this.selectedInputType === 'video') {
          console.log('当前视频源信息:')
          console.log('- 原始视频:', this.selectedVideo)
          console.log('- 预处理路径:', this.preprocessedFilePath)
          console.log('- 最终视频源:', this.getVideoSource())
        }
      }
    },

    // 判断switch是否应该禁用
    isSwitchDisabled() {
      // 如果没有当前文件UUID，禁用switch
      if (!this.fileManager.currentFileUuid) {
        return true
      }

      // 检查当前文件是否有预处理版本
      const hasPreprocessedVersion = this.fileManager.preprocessedFiles.some(
        file => file.uuid === this.fileManager.currentFileUuid
      )

      // 如果没有预处理版本，禁用switch
      return !hasPreprocessedVersion
    },

    imageZoomStyle() {
      return {
        transform: `scale(${this.imagePreview.zoom}) rotate(${this.imagePreview.rotation}deg) translate(${this.imagePreview.panX}px, ${this.imagePreview.panY}px)`,
        cursor: this.imagePreview.zoom > 1 ? (this.imagePreview.isDragging ? 'grabbing' : 'grab') : 'default',
        transition: this.imagePreview.isDragging ? 'none' : 'transform 0.2s ease'
      }
    },
    originalImageStyle() {
      return {
        transform: `scale(${this.originalImageZoom}) translate(${this.originalImagePanX}px, ${this.originalImagePanY}px)`,
        cursor: this.originalImageZoom > 1 ? (this.detailsImageDragging ? 'grabbing' : 'grab') : 'default',
        transition: this.detailsImageDragging ? 'none' : 'transform 0.2s ease'
      }
    },
    processedImageStyle() {
      return {
        transform: `scale(${this.processedImageZoom}) translate(${this.processedImagePanX}px, ${this.processedImagePanY}px)`,
        cursor: this.processedImageZoom > 1 ? (this.detailsImageDragging ? 'grabbing' : 'grab') : 'default',
        transition: this.detailsImageDragging ? 'none' : 'transform 0.2s ease'
      }
    },

    // RTSP 连接相关计算属性
    canTestConnection() {
      return this.activeConfigTab === 'quick' ?
        this.rtspConfig.url.trim() !== '' :
        this.rtspConfig.host.trim() !== ''
    },

    canConnect() {
      return this.canTestConnection && this.rtspConnectionStatus !== 'connecting'
    },

    fullRtspUrl() {
      if (this.activeConfigTab === 'quick') {
        return this.rtspConfig.url.startsWith('rtsp://') ?
          this.rtspConfig.url :
          `rtsp://${this.rtspConfig.url}`
      } else {
        const { host, port, username, password, path } = this.rtspConfig
        const auth = username && password ? `${username}:${password}@` : ''
        return `rtsp://${auth}${host}:${port}${path}`
      }
    },
    resultDetailsImageStyle() {
      return {
        transform: `scale(${this.resultDetailsZoom}) translate(${this.resultDetailsPanX}px, ${this.resultDetailsPanY}px)`,
        cursor: this.resultDetailsZoom > 1 ? (this.resultDetailsDragging ? 'grabbing' : 'grab') : 'default',
        transition: this.resultDetailsDragging ? 'none' : 'transform 0.2s ease'
      }
    },
    hasSelectedResults() {
      return this.detectionResults.some(result => result.selected)
    }
  },
  methods: {
    async startSmallInit(){
        // 检查是否有上传的文件
        if (!this.uploadedFilePath && !this.uploadedFilePaths?.length) {
          this.$message('请先上传训练模板再进行小样本预训练')
          return
        }
 
        if (this.selectedInputType !== 'image') {
          this.$message('请选择图像作为训练模板')
          return
        }
        const response = await APIInitSmall({
              file_type: this.selectedInputType,
              file_uuid: this.fileManager.currentFileUuid,
              input_source: this.fileManager.inputSource,
              model: this.selectedModel
            })
        if (response.code === 200) {
            this.smallModelInit = true;
        } else {
          throw new Error(response.message || '预训练失败')
        }
    },
    // ==================== 通用工具函数 ====================

    // 统一的文件处理工具
    async processFile(file, type, options = {}) {
      try {
        // 清理旧缓存
        if (this.mediaManager) {
          this.mediaManager.clearFileCache(file)
        }

        let processedFile
        if (type === 'image') {
          processedFile = await this.mediaManager.processImage(file, {
            maxWidth: options.maxWidth || 1920,
            maxHeight: options.maxHeight || 1080
          })

          if (processedFile.compressed) {
            // const originalSize = this.mediaManager.formatFileSize(processedFile.originalFile.size)
            // const compressedSize = this.mediaManager.formatFileSize(processedFile.file.size)
            // this.showMessage('success', `图片已优化：${originalSize} → ${compressedSize}`)
          }
        } else if (type === 'video') {
          processedFile = await this.mediaManager.processVideo(file)

          // const fileSize = this.mediaManager.formatFileSize(processedFile.file.size)
          // const duration = processedFile.info.duration ? `${Math.round(processedFile.info.duration)}秒` : '未知'
          // const resolution = `${processedFile.info.width}x${processedFile.info.height}`
          // this.showMessage('success', `视频加载成功：${resolution}, ${duration}, ${fileSize}`)
        }

        return processedFile
      } catch (error) {
        this.handleError('文件处理失败', error)
        throw error
      }
    },

    // 统一的错误处理
    handleError(message, error) {
      console.error(message + ':', error)
      this.showMessage('error', message + ': ' + error.message)
    },

    // 统一的消息显示
    showMessage(type, message) {
      this.$message[type](message)
    },

    // 统一的缓存管理
    getCachedValue(cacheMap, key, computeFn) {
      if (!cacheMap.has(key)) {
        cacheMap.set(key, computeFn())
      }
      return cacheMap.get(key)
    },

    // ==================== 原有方法 ====================

    goBack() {
      this.$router.go(-1)
    },

    preprocessedVideoUrl() {
      // 如果有预处理后的视频路径，返回完整URL
      if (this.preprocessedFilePath && this.selectedInputType === 'video') {
        // 确保路径格式正确，处理Windows路径分隔符
        const normalizedPath = this.preprocessedFilePath.replace(/\\/g, '/')
        const videoUrl = `${serverAddress}${normalizedPath}`
        console.log(`预处理视频URL: ${videoUrl}`)
        return videoUrl
      }
      // 否则返回原始视频
      console.log(`使用原始视频: ${this.selectedVideo}`)
      return this.selectedVideo
    },

    // 获取视频源（根据输入源选择）
    getVideoSource() {
      if (this.fileManager.inputSource === 'preprocessed' && this.preprocessedFilePath) {
        // 确保路径格式正确，处理Windows路径分隔符
        const normalizedPath = this.preprocessedFilePath.replace(/\\/g, '/')
        const videoUrl = `${serverAddress}${normalizedPath}`
        console.log(`获取预处理视频源: ${videoUrl}`)
        console.log(`原始路径: ${this.preprocessedFilePath}`)
        return videoUrl
      }
      console.log(`获取原始视频源: ${this.selectedVideo}`)
      return this.selectedVideo
    },
    // 统一的文件上传处理函数
    async uploadFilesToBackend(files) {
      try {
        // 显示上传中状态
        this.isUploading = true

        // 创建FormData
        const formData = new FormData()

        // 添加文件到FormData
        for (let i = 0; i < files.length; i++) {
          formData.append('files', files[i])
        }

        // 调用后端API
        const response = await APIUploadData(formData)

        if (response && response.status === 'success') {
          // 更新文件管理状态
          if (response.data.file_info) {
            // 单个文件
            this.fileManager.uploadedFiles = [response.data.file_info]
            this.fileManager.currentFileUuid = response.data.file_info.uuid
            this.fileManager.inputSource = 'original' // 重新上传文件后默认使用原文件
            this.fileManager.preprocessedFiles = [] // 清空预处理文件列表

            // 保持向后兼容
            this.uploadedFilePath = response.data.file_info.file_path
            this.uploadedFilePaths = [response.data.file_info.file_path]

            // 显示图像类型信息
            if (response.data.file_info.image_type) {
              const imageTypeText = response.data.file_info.image_type === 'infrared' ? '红外图像' : '可见光图像'
              console.log(`图像类型判别结果: ${imageTypeText}`)
              this.$message.success(`文件上传成功: ${response.data.file_info.original_name}`)
            } else {
              this.$message.success(`文件上传成功: ${response.data.file_info.original_name}`)
            }
          } else if (response.data.files) {
            // 多个文件
            this.fileManager.uploadedFiles = response.data.files
            this.fileManager.currentFileUuid = response.data.files[0]?.uuid
            this.fileManager.inputSource = 'original' // 重新上传文件后默认使用原文件
            this.fileManager.preprocessedFiles = [] // 清空预处理文件列表

            // 保持向后兼容
            this.uploadedFilePaths = response.data.files.map(f => f.file_path)
            this.uploadedFilePath = this.uploadedFilePaths[0]

            // // 统计图像类型
            // const visibleCount = response.data.files.filter(f => f.image_type === 'visible').length
            // const infraredCount = response.data.files.filter(f => f.image_type === 'infrared').length

            // let message = `文件上传成功，共${response.data.files.length}个文件`
            // if (visibleCount > 0 && infraredCount > 0) {
            //   message += ` (${visibleCount}张可见光图像，${infraredCount}张红外图像)`
            // } else if (infraredCount > 0) {
            //   message += ` (${infraredCount}张红外图像)`
            // } else if (visibleCount > 0) {
            //   message += ` (${visibleCount}张可见光图像)`
            // }

            // this.$message.success(message)
          }

          console.log('文件管理状态更新:', this.fileManager)
          console.log('输入源已重置为原文件，switch已禁用')
          return response.data
        } else {
          throw new Error(response?.message || '文件上传失败')
        }
      } catch (error) {
        this.$message.error(`文件上传失败: ${error.message || '未知错误'}`)
        throw error
      } finally {
        this.isUploading = false
      }
    },

    // Input type selection
    selectInputType(type) {
      this.selectedInputType = type
      this.resetInputData()
    },

    resetInputData() {
      // 清理媒体资源（使用媒体管理器）
      if (this.mediaManager) {
        this.mediaManager.cleanup()
      }

      this.selectedImage = null
      this.selectedImageFile = null
      this.selectedVideo = null
      this.selectedVideoFile = null
      this.previewsrclist = []
      this.currentImageIndex = 0
      this.rtspUrl = null
      this.rtspInputUrl = ''

      // 重置文件输入框的值，确保可以重新选择相同文件
      this.$nextTick(() => {
        if (this.$refs.imageInput) {
          this.$refs.imageInput.value = ''
        }
        if (this.$refs.videoInput) {
          this.$refs.videoInput.value = ''
        }
        if (this.$refs.folderInput) {
          this.$refs.folderInput.value = ''
        }
      })

      // 清空上传和预处理结果
      this.uploadedFilePath = null
      this.uploadedFilePaths = []
      this.preprocessedFilePath = null
      this.preprocessedFilePaths = []

      // 重置文件管理状态
      this.fileManager.uploadedFiles = []
      this.fileManager.preprocessedFiles = []
      this.fileManager.currentFileUuid = null
      this.fileManager.inputSource = 'original' // 默认使用原文件

      // 停止视频实时检测
      this.stopVideoRealTimeDetection()

      console.log('文件管理状态已重置，默认使用原文件')
    },

    // File upload handlers
    triggerFileUpload(type) {
      if (type === 'image') {
        this.$refs.imageInput.click()
      } else if (type === 'folder') {
        this.$refs.folderInput.click()
      } else if (type === 'video') {
        this.$refs.videoInput.click()
      }
    },

    // 清除预处理状态
    clearPreprocessedState() {
      this.preprocessedFilePath = null
      this.preprocessedFilePaths = []
      this.fileManager.preprocessedFiles = []
      console.log('已清除预处理状态')
    },

    // 统一的文件上传处理方法
    async handleFileUpload(event, type) {
      const files = event.target.files
      if (!files || files.length === 0) return

      try {
        if (type === 'folder') {
          await this.handleMultipleFiles(Array.from(files))
        } else {
          await this.handleSingleFile(files[0], type)
        }
      } catch (error) {
        this.handleError(`${type}上传失败`, error)
      }
    },

    // 处理单个文件
    async handleSingleFile(file, type) {
      // 清除预处理状态
      this.clearPreprocessedState()

      const processedFile = await this.processFile(file, type)

      if (type === 'image') {
        this.selectedImage = processedFile.url
        this.selectedImageFile = processedFile.file
      } else if (type === 'video') {
        // 清理之前的视频URL
        if (this.selectedVideo) {
          URL.revokeObjectURL(this.selectedVideo)
        }
        this.selectedVideo = processedFile.url
        this.selectedVideoFile = processedFile.file
        this.videoLoading = true
        this.videoError = false
      }

      // 上传到后端
      await this.uploadFilesToBackend([processedFile.file])
    },

    // 处理多个文件（文件夹上传）
    async handleMultipleFiles(files) {
      // 清除预处理状态
      this.clearPreprocessedState()

      const imageFiles = files.filter(file => file.type.startsWith('image/'))

      if (imageFiles.length === 0) {
        this.showMessage('warning', '文件夹中没有找到图片文件')
        return
      }

      this.previewsrclist = []
      const processedFiles = []
      let totalOriginalSize = 0
      let totalCompressedSize = 0

      this.showMessage('info', `正在处理 ${imageFiles.length} 个图片文件...`)

      for (const file of imageFiles) {
        try {
          const processedImage = await this.processFile(file, 'image')
          this.previewsrclist.push(processedImage.url)
          processedFiles.push(processedImage.file)

          totalOriginalSize += processedImage.originalFile.size
          totalCompressedSize += processedImage.file.size
        } catch (error) {
          // 跳过有问题的文件，继续处理其他文件
        }
      }

      // 显示优化结果
      if (totalOriginalSize > totalCompressedSize) {
        const originalSize = this.mediaManager.formatFileSize(totalOriginalSize)
        const compressedSize = this.mediaManager.formatFileSize(totalCompressedSize)
        const savedPercent = ((totalOriginalSize - totalCompressedSize) / totalOriginalSize * 100).toFixed(1)
        this.showMessage('success', `文件夹处理完成：${originalSize} → ${compressedSize} (节省 ${savedPercent}%)`)
      }

      this.currentImageIndex = 0

      if (processedFiles.length > 0) {
        await this.uploadFilesToBackend(processedFiles)
      }
    },

    // 兼容性方法（保持原有调用方式）
    async handleImageUpload(event) {
      await this.handleFileUpload(event, 'image')
    },

    // 兼容性方法（保持原有调用方式）
    async handleFolderUpload(event) {
      await this.handleFileUpload(event, 'folder')
    },

    async handleVideoUpload(event) {
      await this.handleFileUpload(event, 'video')
    },

    // Image/Video management
    openImagePreview() {
      if (this.selectedInputType === 'image' && this.selectedImage) {
        this.imagePreview.currentImage = this.selectedImage
        this.imagePreview.currentIndex = 0
      } else if (this.selectedInputType === 'folder' && this.previewsrclist.length > 0) {
        this.imagePreview.currentIndex = this.currentImageIndex
        this.imagePreview.currentImage = this.previewsrclist[this.imagePreview.currentIndex]
      }
      this.resetImageZoom()
      this.imagePreview.show = true
    },

    closeImagePreview() {
      this.imagePreview.show = false
      this.imagePreview.currentImage = null
      this.resetImageZoom()
    },

    resetImageZoom() {
      Object.assign(this.imagePreview, {
        zoom: 1,
        rotation: 0,
        panX: 0,
        panY: 0,
        isDragging: false,
        dragStartX: 0,
        dragStartY: 0
      })
    },

    downloadCurrentImage() {
      if (this.imagePreview.currentImage) {
        const link = document.createElement('a')
        link.href = this.imagePreview.currentImage
        link.download = this.getImageName(this.imagePreview.currentIndex) || 'image.jpg'
        link.click()
      }
    },

    // Image preview navigation
    previousPreviewImage() {
      if (this.imagePreview.currentIndex > 0) {
        this.imagePreview.currentIndex--
        this.imagePreview.currentImage = this.previewsrclist[this.imagePreview.currentIndex]
        this.resetImageZoom()
      }
    },

    nextPreviewImage() {
      if (this.imagePreview.currentIndex < this.previewsrclist.length - 1) {
        this.imagePreview.currentIndex++
        this.imagePreview.currentImage = this.previewsrclist[this.imagePreview.currentIndex]
        this.resetImageZoom()
      }
    },

    // 统一的图像缩放和拖拽控制
    zoomIn() {
      this.imagePreview.zoom = Math.min(this.imagePreview.zoom * 1.2, 5)
    },

    zoomOut() {
      this.imagePreview.zoom = Math.max(this.imagePreview.zoom / 1.2, 0.1)
      if (this.imagePreview.zoom <= 1) {
        this.imagePreview.panX = 0
        this.imagePreview.panY = 0
      }
    },

    resetZoom() {
      this.resetImageZoom()
    },

    rotateImage(degrees) {
      this.imagePreview.rotation += degrees
    },

    handleImageWheel(event) {
      event.preventDefault()
      if (event.deltaY < 0) {
        this.zoomIn()
      } else {
        this.zoomOut()
      }
    },

    startImageDrag(event) {
      if (this.imagePreview.zoom > 1) {
        event.preventDefault()
        this.imagePreview.isDragging = true
        this.imagePreview.dragStartX = event.clientX - this.imagePreview.panX
        this.imagePreview.dragStartY = event.clientY - this.imagePreview.panY

        // 使用事件管理器添加全局事件监听器
        this.eventManager.addEventListener(document, 'mousemove', this.detailhandleImageDrag)
        this.eventManager.addEventListener(document, 'mouseup', this.detailendImageDrag)
      }
    },

    detailhandleImageDrag(event) {
      if (this.imagePreview.isDragging && this.imagePreview.zoom > 1) {
        event.preventDefault()
        this.imagePreview.panX = event.clientX - this.imagePreview.dragStartX
        this.imagePreview.panY = event.clientY - this.imagePreview.dragStartY
      }
    },

    detailendImageDrag() {
      if (this.imagePreview.isDragging) {
        this.imagePreview.isDragging = false
        // 移除特定的事件监听器
        this.eventManager.removeEventListener('HTMLDocument-mousemove-detailhandleImageDrag')
        this.eventManager.removeEventListener('HTMLDocument-mouseup-detailendImageDrag')
      }
    },

    // Helper methods
    getPreviewTitle() {
      if (this.selectedInputType === 'folder') {
        return `图像预览 (${this.imagePreview.currentIndex + 1}/${this.previewsrclist.length})`
      }
      return '图像预览'
    },

    getImageName(index) {
      if (this.selectedInputType === 'folder' && this.previewsrclist[index]) {
        return `图像_${index + 1}.jpg`
      }
      return 'image.jpg'
    },

    removeImage() {
      this.selectedImage = null
      this.selectedImageFile = null
    },

    removeVideo() {
      // 停止实时检测
      this.stopVideoRealTimeDetection()

      if (this.selectedVideo) {
        URL.revokeObjectURL(this.selectedVideo)
      }
      this.selectedVideo = null
      this.selectedVideoFile = null
      this.videoLoading = false
      this.videoError = false
    },

    // 开始视频实时检测
    startVideoRealTimeDetection() {
      if (this.videoRealTimeDetection.isActive) {
        return // 已经在检测中
      }
      if(this.selectedModel == "small" && !this.smallModelInit){
        this.$message("还未对模型进行预训练")
        return
      }

      console.log('开始视频实时检测')
      this.videoRealTimeDetection.isActive = true
      this.videoRealTimeDetection.detectionResults = []
      this.videoRealTimeDetection.totalDetections = 0
      this.videoRealTimeDetection.lastTimestamp = 0

      // 启动定时器，每50ms发送当前视频时间戳
      this.startDetectionTimer()

      this.$message.success('视频实时检测已启动')
    },

    // 启动检测定时器
    startDetectionTimer() {
      if (this.videoRealTimeDetection.intervalId) {
        clearInterval(this.videoRealTimeDetection.intervalId)
      }

      this.videoRealTimeDetection.intervalId = setInterval(() => {
        this.sendTimestampForDetection()
      }, this.videoRealTimeDetection.frameInterval)

      console.log(`检测定时器已启动，间隔: ${this.videoRealTimeDetection.frameInterval}ms`)
    },

    // 停止检测定时器
    stopDetectionTimer() {
      if (this.videoRealTimeDetection.intervalId) {
        clearInterval(this.videoRealTimeDetection.intervalId)
        this.videoRealTimeDetection.intervalId = null
        console.log('检测定时器已停止')
      }
    },

    // 停止视频实时检测
    stopVideoRealTimeDetection() {
      if (!this.videoRealTimeDetection.isActive) {
        return // 没有在检测中
      }

      console.log('停止视频实时检测')
      this.videoRealTimeDetection.isActive = false

      // 停止定时器
      this.stopDetectionTimer()

      console.log(`实时检测结束，共检测到 ${this.videoRealTimeDetection.totalDetections} 个目标`)
      this.$message.info(`实时检测已停止，共检测到 ${this.videoRealTimeDetection.totalDetections} 个目标`)
    },

    // 发送时间戳进行检测
    async sendTimestampForDetection() {

      // 检查是否正在拖拽或暂停
      if (this.videoRealTimeDetection.isVideoDragging || this.videoRealTimeDetection.isVideoPaused) {
        return
      }

      const video = this.$refs.videoElement
      if (!video || !this.selectedVideoFile) {
        return
      }

      const currentTimestamp = video.currentTime

      // 避免重复检测相同时间戳
      if (Math.abs(currentTimestamp - this.videoRealTimeDetection.lastTimestamp) < 0.01) {
        return
      }

      this.videoRealTimeDetection.lastTimestamp = currentTimestamp

      try {
        console.log(`发送时间戳检测请求: ${currentTimestamp.toFixed(2)}s`)

        // 获取当前文件信息
        const currentFile = this.fileManager.uploadedFiles.find(f => f.uuid === this.fileManager.currentFileUuid)
        if (!currentFile) {
          console.error('未找到当前文件信息')
          return
        }

        // 构建请求数据
        const requestData = {
          timestamp: currentTimestamp,
          video_file: currentFile.original_name,
          file_uuid: this.fileManager.currentFileUuid,
          input_source: this.fileManager.inputSource,
          image_type: currentFile.image_type || 'visible'
        }

        // 如果使用预处理文件，添加预处理路径
        if (this.fileManager.inputSource === 'preprocessed' && this.preprocessedFilePath) {
          requestData.preprocessed_path = this.preprocessedFilePath
        }

        console.log('发送检测请求数据:', requestData)

        const response = await APIDetectVideoTimestamp(requestData)

        if (response.status === 'success') {
          this.handleVideoTimestampDetectionResult(response.data)
        } else {
          console.error('时间戳检测失败:', response.message)
        }

      } catch (error) {
        console.error('时间戳检测请求失败:', error)
      }
    },

    // 对视频进行检测
    handleVideoTimestampDetectionResult(data) {
      const { timestamp, detections, total_detections, image_path, origin_image } = data

      console.log(`视频时间戳检测结果: 时间=${timestamp.toFixed(2)}s, 检测数=${total_detections}`)

      // 只有检测结果大于0时才添加到结果列表
      if (total_detections > 0 && detections && detections.length > 0) {
        // 创建检测结果对象
        const detectionResult = {
          id: crypto.randomUUID(),
          origin_image: origin_image,
          selected: false,
          image: image_path,
          detections: detections,
          timestamp: timestamp,
          frameTime: timestamp
        }

        // 根据显示模式处理结果
        if (this.resultDisplayMode === 'single') {
          this.detectionResults = [detectionResult]
        } else {
          this.detectionResults = [detectionResult, ...this.detectionResults]
        }

        // 限制结果列表长度，避免内存占用过多
        if (this.detectionResults.length > 200) {
          this.detectionResults = this.detectionResults.slice(0, 200)
        }

        console.log(`检测结果已添加，当前结果数量: ${this.detectionResults.length}`)
      } else {
        console.log(`时间戳 ${timestamp.toFixed(2)}s 未检测到目标，跳过添加到结果列表`)
      }

      // 更新统计（无论是否有检测结果都要更新）
      this.videoRealTimeDetection.totalDetections += total_detections
    },

    // Video event handlers
    onVideoLoaded() {
      this.videoLoading = false
      this.videoError = false
    },

    onVideoError(event) {
      this.videoLoading = false
      this.videoError = true

      const video = event.target
      const videoSrc = video.src
      console.error('视频加载失败:', videoSrc)
      console.error('错误详情:', video.error)

      // 输出更详细的错误信息
      if (video.error) {
        const errorMessages = {
          1: 'MEDIA_ERR_ABORTED - 视频加载被中止',
          2: 'MEDIA_ERR_NETWORK - 网络错误',
          3: 'MEDIA_ERR_DECODE - 视频解码错误',
          4: 'MEDIA_ERR_SRC_NOT_SUPPORTED - 视频格式不支持'
        }
        const errorMsg = errorMessages[video.error.code] || `未知错误 (${video.error.code})`
        console.error('视频错误类型:', errorMsg)

        if (video.error.code === 4) {
          this.$message.error('视频格式不支持，请检查预处理是否成功')
        } else if (video.error.code === 2) {
          this.$message.error('视频加载网络错误，请检查文件路径')
        }
      }
    },

    onVideoCanPlay() {
      this.videoLoading = false
      this.videoError = false
    },

    // 视频播放事件处理
    onVideoPlay() {
      console.log('视频开始播放')
      this.videoRealTimeDetection.isVideoPaused = false

      // 如果实时检测已激活，重新启动定时器
      if (this.videoRealTimeDetection.isActive) {
        this.startDetectionTimer()
      }
    },

    // 视频暂停事件处理
    onVideoPause() {
      console.log('视频暂停播放')
      this.videoRealTimeDetection.isVideoPaused = true

      // 暂停时停止检测定时器
      if (this.videoRealTimeDetection.isActive) {
        this.stopDetectionTimer()
      }
    },

    // 视频拖拽开始
    onVideoSeeking() {
      console.log('视频拖拽开始')
      this.videoRealTimeDetection.isVideoDragging = true

      // 拖拽时停止检测定时器
      if (this.videoRealTimeDetection.isActive) {
        this.stopDetectionTimer()
      }
    },

    // 视频拖拽结束
    onVideoSeeked() {
      console.log('视频拖拽结束')
      this.videoRealTimeDetection.isVideoDragging = false

      // 拖拽结束后，如果视频正在播放且实时检测已激活，重新启动定时器
      if (this.videoRealTimeDetection.isActive && !this.videoRealTimeDetection.isVideoPaused) {
        this.startDetectionTimer()
      }
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    removeCurrentImage() {
      if (this.previewsrclist.length > 0) {
        // 删除当前图像
        this.previewsrclist.splice(this.currentImageIndex, 1)

        // 调整当前索引
        if (this.currentImageIndex >= this.previewsrclist.length) {
          this.currentImageIndex = Math.max(0, this.previewsrclist.length - 1)
        }

        // 如果没有图像了，重置状态
        if (this.previewsrclist.length === 0) {
          this.currentImageIndex = 0
        }
      }
    },

    previousImage() {
      if (this.currentImageIndex > 0) {
        this.currentImageIndex--
        // 同步预处理结果导航
        this.syncPreprocessNavigation()
      }
    },

    nextImage() {
      if (this.currentImageIndex < this.previewsrclist.length - 1) {
        this.currentImageIndex++
        // 同步预处理结果导航
        this.syncPreprocessNavigation()
      }
    },

    // 同步预处理结果导航
    syncPreprocessNavigation() {
      if (this.selectedInputType === 'folder') {
        this.currentPreprocessIndex = this.currentImageIndex
        this.currentDetectionIndex = this.currentImageIndex
        console.log(`同步导航: 当前图片索引 ${this.currentImageIndex}`)
      }
    },

    // Enhanced RTSP handling
    getConnectionStatusText() {
      const statusMap = {
        'disconnected': '未连接',
        'connecting': '连接中...',
        'connected': '已连接',
        'error': '连接失败'
      }
      return statusMap[this.rtspConnectionStatus] || '未知状态'
    },

    formatConnectionTime() {
      if (!this.rtspConnectionTime) return ''
      const now = new Date()
      const diff = Math.floor((now - this.rtspConnectionTime) / 1000)
      if (diff < 60) return `${diff}秒`
      if (diff < 3600) return `${Math.floor(diff / 60)}分钟`
      return `${Math.floor(diff / 3600)}小时`
    },

    applyPreset(preset) {
      const { host, username, password, port } = this.rtspConfig
      let url = preset.template
        .replace('{host}', host || '*************')
        .replace('{username}', username || 'admin')
        .replace('{password}', password || 'password')
        .replace('{port}', port || '554')

      // Remove rtsp:// prefix if exists since we'll add it in computed property
      url = url.replace('rtsp://', '')
      this.rtspConfig.url = url
      this.activeConfigTab = 'quick'
    },

    async testConnection() {
      this.rtspConnectionStatus = 'connecting'
      this.connectionTestResult = null

      try {
        // Simulate connection test
        await new Promise(resolve => setTimeout(resolve, 2000))

        // Mock success/failure based on URL format
        const url = this.fullRtspUrl
        if (url.includes('172.10') || url.includes('localhost')) {
          this.connectionTestResult = {
            type: 'success',
            message: '连接测试成功！流媒体服务器响应正常。'
          }
          this.rtspConnectionStatus = 'connected'
        } else {
          throw new Error('无法连接到指定的RTSP服务器')
        }
      } catch (error) {
        this.connectionTestResult = {
          type: 'error',
          message: `连接测试失败：${error.message}`
        }
        this.rtspConnectionStatus = 'error'
      }
    },

    async connectRtsp() {
      this.rtspConnectionStatus = 'connecting'
      this.connectionTestResult = null

      try {
        // Simulate connection process
        await new Promise(resolve => setTimeout(resolve, 1500))

        this.rtspUrl = this.fullRtspUrl
        this.rtspConnectionStatus = 'connected'
        this.rtspConnectionTime = new Date()
        this.showRtspModal = false
this.webRtcServer = new WebRtcStreamer('realtimeVideo', 'http://127.0.0.1:8000')
    //this.webRtcServer.connect('rtsp://***********:8554/live/wideangle')
    this.webRtcServer.connect(this.rtspUrl)
        this.$message.success('RTSP流连接成功！')
      } catch (error) {
        
        this.rtspConnectionStatus = 'error'
        this.$message.error(`连接失败：${error.message}`)
      }
    },

    disconnectRtsp() {
      this.rtspUrl = null
      this.rtspConnectionStatus = 'disconnected'
      this.rtspConnectionTime = null
      this.connectionTestResult = null
      this.$message.info('RTSP流已断开连接')
    },

    // Reset RTSP config when modal opens
    openRtspModal() {
      // this.$message.info("待开发！！！尽请期待")
      // TODO: 实现RTSP功能
      this.showRtspModal = true
      this.activeConfigTab = 'quick'
      this.connectionTestResult = null
      if (this.rtspConnectionStatus === 'error') {
        this.rtspConnectionStatus = 'disconnected'
      }
    },

    // 预处理
    async preprocessData() {
      try {
        this.isProcessing = true
        this.processingStatus = '预处理中...'
        this.processingProgress = 0
        this.showProcessingComplete = false

        // 确定文件类型
        let fileType = ''
        if (this.selectedInputType === 'image') {
          fileType = 'image'
        } else if (this.selectedInputType === 'video') {
          fileType = 'video'
        } else if (this.selectedInputType === 'folder') {
          fileType = 'image' // 文件夹中的每个文件都按图像处理
        } else {
          throw new Error('请先选择并上传文件')
        }

        // 检查是否有上传的文件
        if (!this.uploadedFilePath && !this.uploadedFilePaths?.length) {
          throw new Error('请先上传文件再进行预处理')
        }

        // 文件夹模式：对每张图片分别进行预处理
        if (this.selectedInputType === 'folder') {
          await this.preprocessFolderImages()
        } else {
          // 单个文件预处理
          await this.preprocessSingleFile(fileType)
        }

      } catch (error) {
        console.error('预处理失败:', error)
        this.$message.error(`预处理失败: ${error.message || '未知错误'}`)
      } finally {
        this.isProcessing = false
      }
    },

    // 预处理文件夹中的所有图片
    async preprocessFolderImages() {
      const uploadedFiles = this.fileManager.uploadedFiles
      if (!uploadedFiles || uploadedFiles.length === 0) {
        throw new Error('没有找到已上传的文件')
      }

      console.log(`开始预处理文件夹，共${uploadedFiles.length}张图片，方法: ${this.selectedPreprocessMethod}`)

      const preprocessedResults = []
      const totalFiles = uploadedFiles.length

      for (let i = 0; i < uploadedFiles.length; i++) {
        const fileInfo = uploadedFiles[i]
        this.processingStatus = `预处理中... (${i + 1}/${totalFiles})`
        this.processingProgress = Math.round((i / totalFiles) * 80) + 10

        try {
          console.log(`预处理第${i + 1}张图片: ${fileInfo.original_name}`)

          const response = await APIPreprocessData({
            file_type: 'image',
            file_uuid: fileInfo.uuid,
            method: this.selectedPreprocessMethod
          })

          if (response.status === 'success' && response.data.file_info) {
            preprocessedResults.push(response.data.file_info)
            console.log(`第${i + 1}张图片预处理完成: ${response.data.file_info.processed_path}`)
          } else {
            console.error(`第${i + 1}张图片预处理失败:`, response)
            // 预处理失败时，保留原文件信息
            preprocessedResults.push({
              uuid: fileInfo.uuid,
              original_name: fileInfo.original_name,
              processed_path: fileInfo.file_path, // 使用原文件路径
              method: this.selectedPreprocessMethod
            })
          }
        } catch (error) {
          console.error(`第${i + 1}张图片预处理异常:`, error)
          // 预处理异常时，保留原文件信息
          preprocessedResults.push({
            uuid: fileInfo.uuid,
            original_name: fileInfo.original_name,
            processed_path: fileInfo.file_path, // 使用原文件路径
            method: this.selectedPreprocessMethod
          })
        }
      }

      // 保存预处理结果
      this.fileManager.preprocessedFiles = preprocessedResults
      this.preprocessedFilePaths = preprocessedResults.map(f => f.processed_path)

      this.processingProgress = 100
      this.processingStatus = '预处理完成'
      this.showProcessingComplete = true

      // 重置当前预处理索引，确保与当前图片索引同步
      this.currentPreprocessIndex = this.currentImageIndex

      this.$message.success(`文件夹预处理完成，共处理${preprocessedResults.length}张图片`)
      console.log('文件夹预处理完成，switch现在可用')
      console.log('预处理文件列表:', preprocessedResults)

      // 使用事件管理器管理定时器 - Auto hide after 2 seconds
      this.eventManager.setTimeout(() => {
        this.showProcessingComplete = false
        this.processingStatus = '就绪'
      }, 2000)
    },

    // 预处理单个文件
    async preprocessSingleFile(fileType) {
      this.processingProgress = 30
      console.log(`开始预处理，方法: ${this.selectedPreprocessMethod}`)

      const response = await APIPreprocessData({
        file_type: fileType,
        file_uuid: this.fileManager.currentFileUuid,
        method: this.selectedPreprocessMethod
      })

      if (response.status === 'success') {
        this.processingProgress = 80

        // 保存预处理结果
        if (response.data.file_info) {
          // 单个文件
          this.fileManager.preprocessedFiles = [response.data.file_info]

          // 保持向后兼容
          this.preprocessedFilePath = response.data.file_info.processed_path
          this.$message.success(`预处理完成: ${response.data.file_info.original_name}`)

          console.log('预处理完成，switch现在可用')
          console.log('预处理文件信息:', response.data.file_info)
          console.log('预处理文件路径:', this.preprocessedFilePath)

          // 如果是视频文件，立即测试URL可访问性
          if (this.selectedInputType === 'video') {
            const normalizedPath = this.preprocessedFilePath.replace(/\\/g, '/')
            const testUrl = `${serverAddress}${normalizedPath}`
            console.log('预处理视频URL测试:', testUrl)

            // 测试URL是否可访问
            fetch(testUrl, { method: 'HEAD' })
              .then(response => {
                if (response.ok) {
                  console.log('预处理视频URL可访问')
                } else {
                  console.error('预处理视频URL不可访问，状态码:', response.status)
                }
              })
              .catch(error => {
                console.error('预处理视频URL测试失败:', error)
              })
          }
        } else if (response.data.files) {
          // 多个文件
          this.fileManager.preprocessedFiles = response.data.files

          // 保持向后兼容
          this.preprocessedFilePaths = response.data.files.map(f => f.processed_path)
          this.$message.success(`预处理完成，共处理${response.data.files.length}个文件`)

          console.log('预处理完成，switch现在可用')
          console.log('预处理文件列表:', response.data.files)
        }

        this.processingProgress = 100
        this.isProcessing = false
        this.processingStatus = '预处理完成'
        this.showProcessingComplete = true

        // 使用事件管理器管理定时器 - Auto hide after 2 seconds
        this.eventManager.setTimeout(() => {
          this.showProcessingComplete = false
          this.processingStatus = '就绪'
          this.processingProgress = 0
        }, 2000)

      } else {
        this.$message.error(`预处理失败: ${response.message || '未知错误'}`)
      }
    },

    // 对文件检测
    async startDetection() {
      if(this.selectedModel == "small" && !this.smallModelInit){
        this.$message("还未对模型进行预训练")
        return
      }
      this.isProcessing = true
      this.processingStatus = '检测中...'
      this.processingProgress = 0
      this.showProcessingComplete = false

      try {
        let response

        // 检查是否为文件夹类型，如果是则进行批量检测
        if (this.selectedInputType === 'folder') {
          response = await this.startFolderBatchDetection()
        } else {
          // 模拟进度更新
          const progressInterval = setInterval(() => {
            if (this.processingProgress < 90) {
              this.processingProgress += 10
            }
          }, 200)

          try {
            // 调用后端检测接口
            response = await APIDetectData({
              file_type: this.selectedInputType,
              file_uuid: this.fileManager.currentFileUuid,
              input_source: this.fileManager.inputSource,
              model: this.selectedModel
            })
          } finally {
            if (progressInterval) {
              // 清除进度更新
              clearInterval(progressInterval)
            }

          }
        }

        this.processingProgress = 100

        if (response.code === 200) {
          // 更新检测结果
          if (response.data && response.data.results) {

            // 将后端返回的结果添加到检测结果列表中
            const newResults = response.data.results.map(result => ({
              id: crypto.randomUUID(), // 生成唯一ID
              selected: false,
              image: result.image,
              origin_image: result.origin_image,
              detections: result.detections,
              timestamp: result.timestamp
            }))

            // 根据显示模式处理结果
            if (this.resultDisplayMode === 'single') {
              // 单次结果模式：清空之前的结果，只显示新结果
              this.detectionResults = newResults
              console.log('单次结果模式：清空之前的结果，显示新检测结果')
            } else {
              // 累计结果模式：将新结果添加到现有结果的前面
              this.detectionResults = [...newResults, ...this.detectionResults]
              console.log('累计结果模式：添加新检测结果到现有结果列表')
            }

          }

          this.isProcessing = false
          this.processingStatus = '检测完成'
          this.showProcessingComplete = true

          // 显示成功消息
          this.$message.success(response.message || '检测完成')

          // 2秒后重置状态
          this.eventManager.setTimeout(() => {
            this.showProcessingComplete = false
            this.processingStatus = '就绪'
            this.processingProgress = 0
          }, 2000)
        } else {
          throw new Error(response.message || '检测失败')
        }
      } catch (error) {
        console.error('检测失败:', error)
        this.isProcessing = false
        this.processingStatus = '检测失败'
        this.processingProgress = 0

        // 显示错误消息
        this.$message.error(error.message || '检测过程中发生错误')

        // 2秒后重置状态
        this.eventManager.setTimeout(() => {
          this.processingStatus = '就绪'
        }, 2000)
      }
    },

    // 对文件夹检测
    async startFolderBatchDetection() {
      let uploadedFiles = this.fileManager.uploadedFiles
      if(this.fileManager.inputSource === 'preprocessed' && this.preprocessedFilePaths){
        uploadedFiles = this.preprocessedFilePaths
        console.log(uploadedFiles)
      }

      if (!uploadedFiles || uploadedFiles.length === 0) {
        throw new Error('没有可用的文件进行批量检测')
      }

      console.log(`开始文件夹批量检测，共${uploadedFiles.length}个文件`)

      try {
        let fileUrls,filePath;
        if(this.fileManager.inputSource === 'preprocessed' && this.preprocessedFilePaths){
        uploadedFiles = this.preprocessedFilePaths
        // 构建文件URL列表
          fileUrls = uploadedFiles.map(file => {
          // 确保文件路径格式正确
          filePath = file
          return filePath.startsWith('/') ? filePath : `/${filePath}`
        })
      } else {
          // 构建文件URL列表
          fileUrls = uploadedFiles.map(file => {
          // 确保文件路径格式正确
          filePath = file.file_path || file.url || file.path
          return filePath.startsWith('/') ? filePath : `/${filePath}`
        })
      }
        

        console.log('批量检测文件列表:', fileUrls)

        // 调用后端批量检测接口
        const response = await APIDetectFloder({
          file_urls: fileUrls,
          current_index: this.currentImageIndex || 0,
          detection: this.selectedModel || 'yolov8'
        })

        if (response && response.success) {
          console.log('文件夹批量检测完成')
          console.log('检测结果:', response)

          // 处理检测结果
          const processedFiles = response.processed_files || []
          const failedFiles = response.failed_files || []

          // 转换为前端需要的格式，仅包含有检测结果的项
          const detectionResults = processedFiles
            .filter(file => {
              // 确保 detection_data 存在且 detections 数组长度大于0
              return file.detection_data?.detections?.length > 0;
            })
            .map((file, index) => ({
              id: `batch_${index}_${Date.now()}`,
              selected: false,
              image: file.processed_url,
              origin_image: file.origin_image,
              detections: file.detection_data.detections, // 已确保存在
              timestamp: Date.now(),
              originalFile: file.original_url,
              statistics: file.detection_data?.statistics || {}
            }))

          console.log(`有效检测结果: ${detectionResults.length}项`)

          // 根据显示模式处理结果
          if (this.resultDisplayMode === 'single') {
            // 单次结果模式：清空之前的结果，只显示新结果
            this.detectionResults = detectionResults
            console.log('单次结果模式：清空之前的结果，显示新检测结果')
          } else {
            // 累计结果模式：将新结果添加到现有结果的前面
            this.detectionResults = [...detectionResults, ...this.detectionResults]
            console.log('累计结果模式：添加新检测结果到现有结果列表')
          }

          // 显示结果统计
          const successCount = response.processed_count || 0
          const failedCount = response.failed_count || 0
          const totalDetections = detectionResults.reduce((sum, result) =>
            sum + (result.detections?.length || 0), 0)

          let message = `批量检测完成：成功 ${successCount} 个，失败 ${failedCount} 个`
          if (totalDetections > 0) {
            message += `，共检测到 ${totalDetections} 个目标`
          }

          this.$message.success(message)

          // 如果有失败的文件，显示警告
          if (failedFiles.length > 0) {
            console.warn('检测失败的文件:', failedFiles)
            const failedNames = failedFiles.map(f => f.error).join(', ')
            this.$message.warning(`部分文件检测失败: ${failedNames}`)
          }

          return {
            code: 200,
            message: "批量检测完成",
            data: {
              results: detectionResults,
              processed_count: successCount,
              failed_count: failedCount,
              total_count: uploadedFiles.length
            }
          }
        } else {
          throw new Error('批量检测失败：后端返回异常')
        }
      } catch (error) {
        console.error('文件夹批量检测失败:', error)

        // 显示详细错误信息
        if (error.response && error.response.data) {
          throw new Error(`批量检测失败: ${error.response.data.message || error.message}`)
        } else {
          throw new Error(`批量检测失败: ${error.message}`)
        }
      }
    },

    toggleRealTimeDetection() {
      this.isRealTimeDetecting = !this.isRealTimeDetecting
      if (this.isRealTimeDetecting) {
        this.startRealTimeProcessing()
      } else {
        this.stopRealTimeProcessing()
      }
    },

    startRealTimeProcessing() {
      // 使用事件管理器管理GPS更新定时器
      this.gpsUpdateInterval = this.eventManager.setInterval(() => {
        this.updateGpsCoordinates()
      }, 1000)
    },

    stopRealTimeProcessing() {
      if (this.gpsUpdateInterval) {
        this.eventManager.clearTimer(this.gpsUpdateInterval)
        this.gpsUpdateInterval = null
      }
    },

    updateGpsCoordinates() {
      // Simulate GPS coordinate updates
      this.currentGpsCoordinates = {
        lat: this.currentGpsCoordinates.lat + (Math.random() - 0.5) * 0.001,
        lng: this.currentGpsCoordinates.lng + (Math.random() - 0.5) * 0.001
      }
      this.flightPath.push({ ...this.currentGpsCoordinates })
    },

    // 地图相关功能
    clearScreen() {
      // 清屏功能 - 清除地图上的所有标记和数据
      console.log('清屏操作')
      // 可以在这里添加清除地图标记的逻辑
      if (this.$refs.mapComponent) {
        // 如果地图组件有清屏方法，调用它
        if (typeof this.$refs.mapComponent.clearMap === 'function') {
          this.$refs.mapComponent.clearMap()
        }
      }
    },

    toggleMarkerLock() {
      // 切换标记锁定状态
      this.markersLocked = !this.markersLocked
      console.log(`标记锁定状态: ${this.markersLocked ? '已锁定' : '已解锁'}`)
    },

    clearFlightPath() {
      // 清除航迹路径
      this.flightPath = []
      console.log('航迹路径已清除')
    },

    handleMarkerClick(marker) {
      // 处理地图标记点击事件
      console.log('地图标记被点击:', marker)
      // 可以在这里添加标记点击的处理逻辑
    },

    // New methods for updated UI
    getFileName(url) {
      if (!url) return ''
      if (url.startsWith('data:')) return '已选择文件'
      return url.split('/').pop() || '未知文件'
    },

    // Preprocessing result navigation
    previousPreprocessResult() {
      if (this.currentPreprocessIndex > 0) {
        this.currentPreprocessIndex--
        // 同步主导航
        this.currentImageIndex = this.currentPreprocessIndex
        this.updatePreprocessCanvas()
        console.log(`预处理导航: 切换到第${this.currentPreprocessIndex + 1}张图片`)
      }
    },

    nextPreprocessResult() {
      if (this.currentPreprocessIndex < this.previewsrclist.length - 1) {
        this.currentPreprocessIndex++
        // 同步主导航
        this.currentImageIndex = this.currentPreprocessIndex
        this.updatePreprocessCanvas()
        console.log(`预处理导航: 切换到第${this.currentPreprocessIndex + 1}张图片`)
      }
    },

    getCurrentPreprocessImage() {
      // 如果没有选择文件或正在重新选择文件，显示默认图像
      if (!this.selectedImage && !this.previewsrclist.length) {
        return require('@/assets/imgs/no-image.png')
      }

      // 优先显示预处理后的结果
      if (this.selectedInputType === 'image' && this.preprocessedFilePath) {
        return `${serverAddress}${this.preprocessedFilePath}`
      } else if (this.selectedInputType === 'folder' && this.preprocessedFilePaths.length > 0) {
        const currentPath = this.preprocessedFilePaths[this.currentPreprocessIndex]
        return currentPath ? `${serverAddress}${currentPath}` : this.previewsrclist[this.currentPreprocessIndex]
      } else if (this.selectedInputType === 'image' && this.selectedImage) {
        return this.selectedImage // 显示原始图像
      } else if (this.selectedInputType === 'folder' && this.previewsrclist.length > 0) {
        return this.previewsrclist[this.currentPreprocessIndex]
      }
      return require('@/assets/imgs/no-image.png')
    },

    // Detection result navigation
    previousDetectionResult() {
      if (this.currentDetectionIndex > 0) {
        this.currentDetectionIndex--
        this.updateDetectionCanvas()
      }
    },

    nextDetectionResult() {
      if (this.currentDetectionIndex < this.previewsrclist.length - 1) {
        this.currentDetectionIndex++
        this.updateDetectionCanvas()
      }
    },

    // Details dialog methods
    showPreprocessDetails() {
      this.showPreprocessDetailsDialog = true
      // 在对话框显示后自动调整图像缩放
      this.$nextTick(() => {
        // 稍微延迟以确保对话框完全渲染
        setTimeout(() => {
          this.fitImagesToContainer()
        }, 300)
      })
    },

    // 确认预处理结果，将其设置为检测输入源
    confirmPreprocessResults() {
      if (!this.hasPreprocessResults) {
        this.$message.warning('没有可用的预处理结果')
        return
      }

      // 切换到预处理输入源
      this.fileManager.inputSource = 'preprocessed'

      // 提供用户反馈
      this.$message.success('已确认预处理结果，检测将使用预处理后的数据')
      console.log('预处理结果已确认，输入源切换为预处理数据')

      // 如果是视频类型，输出当前视频源信息
      if (this.selectedInputType === 'video') {
        console.log('当前视频源信息:')
        console.log('- 原始视频:', this.selectedVideo)
        console.log('- 预处理路径:', this.preprocessedFilePath)
        console.log('- 最终视频源:', this.getVideoSource())
      }
    },

    closePreprocessDetails() {
      this.showPreprocessDetailsDialog = false
      // 重置缩放状态
      this.resetDetailsZoom()
    },
    closeDetectionDetails() {
      this.showDetectionDetailsDialog = false
    },
    // Details dialog helper methods
    getPreprocessDetailsTitle() {
      if (this.selectedInputType === 'folder') {
        return `预处理详情 (${this.currentPreprocessIndex + 1}/${this.previewsrclist.length})`
      }
      return '预处理详情'
    },

    getOriginalImageForDetails() {
      if (this.selectedInputType === 'image' && this.selectedImage) {
        return this.selectedImage
      } else if (this.selectedInputType === 'folder' && this.previewsrclist.length > 0) {
        return this.previewsrclist[this.currentPreprocessIndex]
      }
      return require('@/assets/imgs/no-image.png')
    },

    // Details zoom and navigation methods
    syncZoomIn() {
      this.detailsZoom = Math.min(this.detailsZoom * 1.2, 5)
      this.originalImageZoom = this.detailsZoom
      this.processedImageZoom = this.detailsZoom
    },

    syncZoomOut() {
      this.detailsZoom = Math.max(this.detailsZoom / 1.2, 0.1)
      this.originalImageZoom = this.detailsZoom
      this.processedImageZoom = this.detailsZoom
      if (this.detailsZoom <= 1) {
        this.resetDetailsPan()
      }
    },

    // 以指定点为中心进行缩放
    syncZoomInAtPoint(mouseX, mouseY, containerWidth, containerHeight) {
      const oldZoom = this.detailsZoom
      const newZoom = Math.min(oldZoom * 1.2, 5)

      if (newZoom !== oldZoom) {
        // 计算缩放中心点相对于容器中心的偏移
        const centerX = containerWidth / 2
        const centerY = containerHeight / 2
        const offsetX = mouseX - centerX
        const offsetY = mouseY - centerY

        // 计算新的平移值，使缩放以鼠标位置为中心
        const zoomRatio = newZoom / oldZoom
        this.originalImagePanX = this.originalImagePanX - offsetX * (zoomRatio - 1) / newZoom
        this.originalImagePanY = this.originalImagePanY - offsetY * (zoomRatio - 1) / newZoom
        this.processedImagePanX = this.processedImagePanX - offsetX * (zoomRatio - 1) / newZoom
        this.processedImagePanY = this.processedImagePanY - offsetY * (zoomRatio - 1) / newZoom

        this.detailsZoom = newZoom
        this.originalImageZoom = newZoom
        this.processedImageZoom = newZoom
      }
    },

    syncZoomOutAtPoint(mouseX, mouseY, containerWidth, containerHeight) {
      const oldZoom = this.detailsZoom
      const newZoom = Math.max(oldZoom / 1.2, 0.1)

      if (newZoom !== oldZoom) {
        if (newZoom <= 1) {
          this.resetDetailsPan()
          this.detailsZoom = 1
          this.originalImageZoom = 1
          this.processedImageZoom = 1
        } else {
          // 计算缩放中心点相对于容器中心的偏移
          const centerX = containerWidth / 2
          const centerY = containerHeight / 2
          const offsetX = mouseX - centerX
          const offsetY = mouseY - centerY

          // 计算新的平移值，使缩放以鼠标位置为中心
          const zoomRatio = newZoom / oldZoom
          this.originalImagePanX = this.originalImagePanX - offsetX * (zoomRatio - 1) / newZoom
          this.originalImagePanY = this.originalImagePanY - offsetY * (zoomRatio - 1) / newZoom
          this.processedImagePanX = this.processedImagePanX - offsetX * (zoomRatio - 1) / newZoom
          this.processedImagePanY = this.processedImagePanY - offsetY * (zoomRatio - 1) / newZoom

          this.detailsZoom = newZoom
          this.originalImageZoom = newZoom
          this.processedImageZoom = newZoom
        }
      }
    },

    resetDetailsZoom() {
      this.detailsZoom = 1
      this.originalImageZoom = 1
      this.processedImageZoom = 1
      this.resetDetailsPan()
    },

    // 自适应缩放图像到合适的尺寸
    fitImagesToContainer() {
      this.$nextTick(() => {
        const originalImg = this.$refs.originalDetailImage
        const processedImg = this.$refs.processedDetailImage
        const originalViewer = this.$refs.originalImageViewer
        const processedViewer = this.$refs.processedImageViewer

        if (originalImg && processedImg && originalViewer && processedViewer) {
          // 等待图像加载完成
          const checkImagesLoaded = () => {
            if (originalImg.complete && originalImg.naturalWidth > 0 &&
              processedImg.complete && processedImg.naturalWidth > 0) {

              // 获取容器尺寸
              const containerWidth = originalViewer.clientWidth || 400
              const containerHeight = originalViewer.clientHeight || 300

              console.log(`容器尺寸: ${containerWidth}x${containerHeight}`)

              // 计算原图的适应缩放比例
              const originalScaleX = containerWidth / originalImg.naturalWidth
              const originalScaleY = containerHeight / originalImg.naturalHeight
              const originalScale = Math.min(originalScaleX, originalScaleY, 1) // 不超过原始尺寸

              // 计算处理后图像的适应缩放比例
              const processedScaleX = containerWidth / processedImg.naturalWidth
              const processedScaleY = containerHeight / processedImg.naturalHeight
              const processedScale = Math.min(processedScaleX, processedScaleY, 1) // 不超过原始尺寸

              // 使用较小的缩放比例，确保两张图像显示一致
              const finalScale = Math.min(originalScale, processedScale)

              console.log(`图像自适应缩放比例: ${finalScale.toFixed(3)}`)
              console.log(`原图尺寸: ${originalImg.naturalWidth}x${originalImg.naturalHeight}`)
              console.log(`处理后图像尺寸: ${processedImg.naturalWidth}x${processedImg.naturalHeight}`)

              // 应用缩放
              this.detailsZoom = finalScale
              this.originalImageZoom = finalScale
              this.processedImageZoom = finalScale

              // 重置平移
              this.resetDetailsPan()

            } else {
              // 如果图像还没加载完成，稍后再试
              setTimeout(checkImagesLoaded, 100)
            }
          }

          checkImagesLoaded()
        }
      })
    },

    resetDetailsPan() {
      this.originalImagePanX = 0
      this.originalImagePanY = 0
      this.processedImagePanX = 0
      this.processedImagePanY = 0
    },

    // Image wheel and drag handlers for details
    handleOriginalImageWheel(event) {
      event.preventDefault()
      const rect = event.target.getBoundingClientRect()
      const mouseX = event.clientX - rect.left
      const mouseY = event.clientY - rect.top

      if (event.deltaY < 0) {
        this.syncZoomInAtPoint(mouseX, mouseY, rect.width, rect.height)
      } else {
        this.syncZoomOutAtPoint(mouseX, mouseY, rect.width, rect.height)
      }
    },

    handleProcessedImageWheel(event) {
      event.preventDefault()
      const rect = event.target.getBoundingClientRect()
      const mouseX = event.clientX - rect.left
      const mouseY = event.clientY - rect.top

      if (event.deltaY < 0) {
        this.syncZoomInAtPoint(mouseX, mouseY, rect.width, rect.height)
      } else {
        this.syncZoomOutAtPoint(mouseX, mouseY, rect.width, rect.height)
      }
    },

    startOriginalImageDrag(event) {
      if (this.originalImageZoom > 1) {
        this.detailsImageDragging = true
        this.dragStartX = event.clientX
        this.dragStartY = event.clientY
        this.dragStartPanX = this.originalImagePanX
        this.dragStartPanY = this.originalImagePanY

        // 使用事件管理器添加全局鼠标事件监听器
        this.eventManager.addEventListener(document, 'mousemove', this.handleImageDrag)
        this.eventManager.addEventListener(document, 'mouseup', this.endImageDrag)

        // 防止默认行为和选择文本
        event.preventDefault()
      }
    },

    startProcessedImageDrag(event) {
      if (this.processedImageZoom > 1) {
        this.detailsImageDragging = true
        this.dragStartX = event.clientX
        this.dragStartY = event.clientY
        this.dragStartPanX = this.processedImagePanX
        this.dragStartPanY = this.processedImagePanY

        // 使用事件管理器添加全局鼠标事件监听器
        this.eventManager.addEventListener(document, 'mousemove', this.handleImageDrag)
        this.eventManager.addEventListener(document, 'mouseup', this.endImageDrag)

        // 防止默认行为和选择文本
        event.preventDefault()
      }
    },

    handleImageDrag(event) {
      if (this.detailsImageDragging) {
        const deltaX = event.clientX - this.dragStartX
        const deltaY = event.clientY - this.dragStartY

        // 同时更新两边图像的平移值
        this.originalImagePanX = this.dragStartPanX + deltaX / this.originalImageZoom
        this.originalImagePanY = this.dragStartPanY + deltaY / this.originalImageZoom
        this.processedImagePanX = this.dragStartPanX + deltaX / this.processedImageZoom
        this.processedImagePanY = this.dragStartPanY + deltaY / this.processedImageZoom
      }
    },

    endImageDrag() {
      if (this.detailsImageDragging) {
        this.detailsImageDragging = false

        // 使用事件管理器移除全局鼠标事件监听器
        this.eventManager.removeEventListener('HTMLDocument-mousemove-handleImageDrag')
        this.eventManager.removeEventListener('HTMLDocument-mouseup-endImageDrag')
      }
    },

    // Details navigation
    previousDetailsImage() {
      this.previousPreprocessResult()
      // 切换图片后自动调整缩放
      this.$nextTick(() => {
        setTimeout(() => {
          this.fitImagesToContainer()
        }, 100)
      })
    },

    nextDetailsImage() {
      this.nextPreprocessResult()
      // 切换图片后自动调整缩放
      this.$nextTick(() => {
        setTimeout(() => {
          this.fitImagesToContainer()
        }, 100)
      })
    },

    // Detection details methods
    toggleDetailsDetectionBoxes() {
      this.showDetailsDetectionBoxes = !this.showDetailsDetectionBoxes
      this.updateDetectionDetailsCanvas()
    },

    previousDetectionDetails() {
      this.previousDetectionResult()
    },

    nextDetectionDetails() {
      this.nextDetectionResult()
    },

    updateDetectionDetailsCanvas() {
      // Update detection details canvas
      this.$nextTick(() => {
        this.drawDetectionDetails()
      })
    },

    async syncVideoPlay() {
      if (!this.$refs.originalDetailVideo || !this.$refs.processedDetailVideo) {
        console.warn('视频元素未找到')
        return
      }

      if (this.videoSyncState.isSyncing) return
      this.videoSyncState.isSyncing = true

      try {
        const originalVideo = this.$refs.originalDetailVideo
        const processedVideo = this.$refs.processedDetailVideo

        // 同步到主视频的时间
        const masterTime = this.videoSyncState.masterVideo === 'original'
          ? originalVideo.currentTime
          : processedVideo.currentTime

        originalVideo.currentTime = masterTime
        processedVideo.currentTime = masterTime

        // 等待时间设置完成
        await new Promise(resolve => setTimeout(resolve, 100))

        // 同时播放两个视频
        const promises = []
        if (originalVideo.paused) {
          promises.push(originalVideo.play())
        }
        if (processedVideo.paused) {
          promises.push(processedVideo.play())
        }

        await Promise.all(promises)
        console.log('视频同步播放成功')
      } catch (error) {
        console.error('视频播放失败:', error)
        this.$message.error('视频播放失败，请检查视频文件')
      } finally {
        this.videoSyncState.isSyncing = false
      }
    },

    syncVideoPause() {
      if (!this.$refs.originalDetailVideo || !this.$refs.processedDetailVideo) {
        console.warn('视频元素未找到')
        return
      }

      try {
        this.$refs.originalDetailVideo.pause()
        this.$refs.processedDetailVideo.pause()
        console.log('视频同步暂停成功')
      } catch (error) {
        console.error('视频暂停失败:', error)
      }
    },

    // 逐帧控制方法
    frameByFrame(direction) {
      if (!this.$refs.originalDetailVideo || !this.$refs.processedDetailVideo) {
        console.warn('视频元素未找到')
        return
      }

      try {
        // 暂停视频以进行逐帧操作
        this.syncVideoPause()

        const frameRate = 30
        const frameTime = 1 / frameRate

        const originalVideo = this.$refs.originalDetailVideo
        const processedVideo = this.$refs.processedDetailVideo

        // 以主视频为准计算新时间
        const masterVideo = this.videoSyncState.masterVideo === 'original' ? originalVideo : processedVideo
        let newTime = masterVideo.currentTime + (direction * frameTime)

        // 确保时间在有效范围内
        newTime = Math.max(0, Math.min(newTime, masterVideo.duration || 0))

        // 同步设置两个视频的时间
        originalVideo.currentTime = newTime
        processedVideo.currentTime = newTime

        console.log(`逐帧${direction > 0 ? '前进' : '后退'}到时间: ${newTime.toFixed(3)}s`)
      } catch (error) {
        console.error('逐帧操作失败:', error)
        this.$message.error('逐帧操作失败')
      }
    },

    // 切换主视频
    switchMasterVideo() {
      this.videoSyncState.masterVideo = this.videoSyncState.masterVideo === 'original' ? 'processed' : 'original'
      console.log(`切换主视频为: ${this.videoSyncState.masterVideo === 'original' ? '原视频' : '处理后视频'}`)
      this.$message.success(`已切换主视频为${this.videoSyncState.masterVideo === 'original' ? '原视频' : '处理后视频'}`)
    },


    // Detection results methods
    getUniqueCategories(detections) {
      if (!detections || !Array.isArray(detections)) return []
      return [...new Set(detections.map(d => d.category))]
    },

    formatTimestamp(timestamp) {
      return new Date(timestamp).toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    handleSelectionChange(selection) {
      // Update selected state based on table selection
      this.detectionResults.forEach(result => {
        result.selected = selection.some(item => item.id === result.id)
      })

      // Update allResultsSelected state
      this.updateAllSelectedState()
    },

    toggleAllSelection() {
      this.$nextTick(() => {
        if (this.allResultsSelected) {
          // Select all results
          this.detectionResults.forEach(result => {
            result.selected = true
          })
          // Select all rows in current page
          this.getCachedPaginatedResults().forEach(row => {
            this.$refs.resultsTable?.toggleRowSelection(row, true)
          })
        } else {
          // Deselect all results
          this.detectionResults.forEach(result => {
            result.selected = false
          })
          this.$refs.resultsTable?.clearSelection()
        }
      })
    },

    updateAllSelectedState() {
      const selectedCount = this.detectionResults.filter(result => result.selected).length
      this.allResultsSelected = selectedCount === this.detectionResults.length && this.detectionResults.length > 0
    },

    handleSelectAll(selection) {
      // Element Plus表格原生全选事件处理
      const isSelectAll = selection.length > 0

      // 更新当前页面所有结果的选中状态
      this.getCachedPaginatedResults().forEach(result => {
        result.selected = isSelectAll
      })

      // 如果是全选，需要选中所有页面的数据
      if (isSelectAll) {
        this.detectionResults.forEach(result => {
          result.selected = true
        })
      } else {
        // 如果是取消全选，只取消当前页面的选择
        this.getCachedPaginatedResults().forEach(result => {
          result.selected = false
        })
      }

      this.updateAllSelectedState()
    },

    showResultDetails(result) {
      if (this.getCachedUniqueCategories(result).length === 0) {
        return;
      }
      this.selectedResultForDetails = result
      this.showResultDetailsDialog = true
      
    },

    closeResultDetails() {
      this.showResultDetailsDialog = false
      this.selectedResultForDetails = null
      this.resetResultDetailsZoom()
    },

    getResultDetailsTitle() {
      if (!this.selectedResultForDetails) return '检测结果详情'
      const index = this.detectionResults.findIndex(r => r.id === this.selectedResultForDetails.id)
      return `检测结果详情 (${index + 1}/${this.detectionResults.length})`
    },

    getCurrentResultIndex() {
      if (!this.selectedResultForDetails) return 0
      return this.detectionResults.findIndex(r => r.id === this.selectedResultForDetails.id)
    },

    previousResultDetails() {
      const currentIndex = this.getCurrentResultIndex()
      if (currentIndex > 0) {
        this.selectedResultForDetails = this.detectionResults[currentIndex - 1]
        
      }
    },

    nextResultDetails() {
      const currentIndex = this.getCurrentResultIndex()
      if (currentIndex < this.detectionResults.length - 1) {
        this.selectedResultForDetails = this.detectionResults[currentIndex + 1]
      }
    },

    getCategoryStatistics(detections) {
      if (!detections || !Array.isArray(detections)) return {}
      const stats = {}
      detections.forEach(detection => {
        stats[detection.category] = (stats[detection.category] || 0) + 1
      })
      return stats
    },

    toggleResultAnnotations() {
      console.log(`切换标注显示: ${!this.showAnnotations}, 当前缩放: ${this.resultDetailsZoom}, 平移: (${this.resultDetailsPanX}, ${this.resultDetailsPanY})`)
      this.showAnnotations = !this.showAnnotations
      this.$nextTick(() => {
        // 更新详情对话框
        this.drawResultDetailsCanvas()
      })
    },
    
    resetResultDetailsZoom() {
      this.resultDetailsZoom = 1
      this.resultDetailsPanX = 0
      this.resultDetailsPanY = 0
    },

    handleResultDetailsWheel(event) {
      event.preventDefault()
      const rect = event.target.getBoundingClientRect()
      const mouseX = event.clientX - rect.left
      const mouseY = event.clientY - rect.top

      if (event.deltaY < 0) {
        this.resultDetailsZoomInAtPoint(mouseX, mouseY, rect.width, rect.height)
      } else {
        this.resultDetailsZoomOutAtPoint(mouseX, mouseY, rect.width, rect.height)
      }
    },

    resultDetailsZoomInAtPoint(mouseX, mouseY, containerWidth, containerHeight) {
      const oldZoom = this.resultDetailsZoom
      const newZoom = Math.min(oldZoom * 1.2, 5)

      if (newZoom !== oldZoom) {
        const centerX = containerWidth / 2
        const centerY = containerHeight / 2
        const offsetX = mouseX - centerX
        const offsetY = mouseY - centerY

        const zoomRatio = newZoom / oldZoom
        this.resultDetailsPanX = this.resultDetailsPanX - offsetX * (zoomRatio - 1) / newZoom
        this.resultDetailsPanY = this.resultDetailsPanY - offsetY * (zoomRatio - 1) / newZoom

        this.resultDetailsZoom = newZoom
      }
    },

    resultDetailsZoomOutAtPoint(mouseX, mouseY, containerWidth, containerHeight) {
      const oldZoom = this.resultDetailsZoom
      const newZoom = Math.max(oldZoom / 1.2, 0.1)

      if (newZoom !== oldZoom) {
        if (newZoom <= 1) {
          this.resetResultDetailsZoom()
        } else {
          const centerX = containerWidth / 2
          const centerY = containerHeight / 2
          const offsetX = mouseX - centerX
          const offsetY = mouseY - centerY

          const zoomRatio = newZoom / oldZoom
          this.resultDetailsPanX = this.resultDetailsPanX - offsetX * (zoomRatio - 1) / newZoom
          this.resultDetailsPanY = this.resultDetailsPanY - offsetY * (zoomRatio - 1) / newZoom

          this.resultDetailsZoom = newZoom
        }
      }
    },

    startResultDetailsDrag(event) {
      if (this.resultDetailsZoom > 1) {
        this.resultDetailsDragging = true
        this.resultDetailsDragStartX = event.clientX
        this.resultDetailsDragStartY = event.clientY
        this.resultDetailsDragStartPanX = this.resultDetailsPanX
        this.resultDetailsDragStartPanY = this.resultDetailsPanY

        // 使用事件管理器添加全局事件监听器
        this.eventManager.addEventListener(document, 'mousemove', this.handleResultDetailsDrag)
        this.eventManager.addEventListener(document, 'mouseup', this.endResultDetailsDrag)

        event.preventDefault()
      }
    },

    handleResultDetailsDrag(event) {
      if (this.resultDetailsDragging) {
        const deltaX = event.clientX - this.resultDetailsDragStartX
        const deltaY = event.clientY - this.resultDetailsDragStartY

        this.resultDetailsPanX = this.resultDetailsDragStartPanX + deltaX / this.resultDetailsZoom
        this.resultDetailsPanY = this.resultDetailsDragStartPanY + deltaY / this.resultDetailsZoom
      }
    },

    endResultDetailsDrag() {
      if (this.resultDetailsDragging) {
        this.resultDetailsDragging = false

        // 使用事件管理器移除全局事件监听器
        this.eventManager.removeEventListener('HTMLDocument-mousemove-handleResultDetailsDrag')
        this.eventManager.removeEventListener('HTMLDocument-mouseup-endResultDetailsDrag')
      }
    },

    getCategoryTagType(category) {
      const categoryTypes = {
        '车辆': 'primary',
        '人员': 'success',
        '建筑物': 'info',
        '飞机': 'warning',
        '船舶': 'primary',
        '装甲车': 'danger',
        '雷达': 'warning',
        '直升机': 'warning',
        '坦克': 'danger',
        '导弹发射器': 'danger'
      }
      return categoryTypes[category] || 'primary'
    },

    // 统一的缓存方法
    getCachedUniqueCategories(row) {
      if (!this.categoriesCache) {
        this.categoriesCache = new Map()
      }
      const cacheKey = `${row.id}-${row.detections.length}`
      return this.getCachedValue(this.categoriesCache, cacheKey, () => this.getUniqueCategories(row.detections))
    },

    getCachedFormattedTimestamp(timestamp) {
      if (!this.timestampCache) {
        this.timestampCache = new Map()
      }
      let currentTimestamp
      if(this.selectInputType !== 'video'){
        currentTimestamp = Date.now();
      } else {
        currentTimestamp = this.videoRealTimeDetection.lastTimestamp
      }
      
      return this.getCachedValue(this.timestampCache, currentTimestamp, () => this.formatTimestamp(currentTimestamp))
    },

    // 性能优化工具方法
    debounce(func, wait) {
      let timeout
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout)
          func.apply(this, args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
      }
    },

    handleResizeObserverError() {
      // 更彻底的 ResizeObserver 错误处理

      // 重写 ResizeObserver 以避免循环错误
      if (window.ResizeObserver) {
        const OriginalResizeObserver = window.ResizeObserver
        const self = this
        window.ResizeObserver = class extends OriginalResizeObserver {
          constructor(callback) {
            const debouncedCallback = self.debounce(callback, 16) // 16ms = 60fps
            super((entries, observer) => {
              try {
                debouncedCallback(entries, observer)
              } catch (error) {
                if (!error.message.includes('ResizeObserver loop completed')) {
                  throw error
                }
              }
            })
          }
        }
      }

      // 全局错误处理
      const originalConsoleError = console.error
      console.error = (...args) => {
        if (args[0] && typeof args[0] === 'string' &&
          args[0].includes('ResizeObserver loop completed with undelivered notifications')) {
          return // 忽略 ResizeObserver 错误
        }
        originalConsoleError.apply(console, args)
      }

      // 使用事件管理器处理 webpack-dev-server 的错误覆盖
      this.eventManager.addEventListener(window, 'error', (event) => {
        if (event.error && event.error.message &&
          event.error.message.includes('ResizeObserver loop completed')) {
          event.preventDefault()
          event.stopImmediatePropagation()
          return false
        }
      }, true)

      // 使用事件管理器处理未捕获的 Promise 错误
      this.eventManager.addEventListener(window, 'unhandledrejection', (event) => {
        if (event.reason && event.reason.message &&
          event.reason.message.includes('ResizeObserver')) {
          event.preventDefault()
          return false
        }
      })
    },

    onDetailsImageLoad() {
      // 详情图像加载成功后重新计算canvas尺寸并绘制检测框
      const img = this.$refs.resultDetailsImage
      console.log(`详情图像加载成功: ${this.selectedResultForDetails?.image}, 尺寸: ${img?.naturalWidth}x${img?.naturalHeight}`)
    },

    onDetailsImageError() {
      // 详情图像加载失败处理
      console.error(`详情图像加载失败: ${this.selectedResultForDetails?.image}`)
    },

    drawResultDetailsCanvas() {
      if (!this.selectedResultForDetails || !this.$refs.resultDetailsCanvas || !this.$refs.resultDetailsImage) return

      // 使用事件管理器管理动画帧优化渲染
      if (this.canvasRenderFrame) {
        this.eventManager.cancelAnimationFrame(this.canvasRenderFrame)
      }

      this.canvasRenderFrame = this.eventManager.requestAnimationFrame(() => {
        const img = this.$refs.resultDetailsImage

        if (!img || !img.complete || img.naturalWidth === 0) return

      })
    },

    // 将YOLO格式坐标转换为像素坐标
    convertYoloToPixelCoords(boundingBox, imageWidth, imageHeight) {
      if (!boundingBox || !imageWidth || !imageHeight) {
        return { x: 0, y: 0, width: 0, height: 0 }
      }

      // YOLO格式: (center_x, center_y, width, height) 都是0-1的归一化值
      const centerX = boundingBox.center_x * imageWidth
      const centerY = boundingBox.center_y * imageHeight
      const boxWidth = boundingBox.width * imageWidth
      const boxHeight = boundingBox.height * imageHeight

      // 转换为左上角坐标格式
      const x = Math.round(centerX - boxWidth / 2)
      const y = Math.round(centerY - boxHeight / 2)
      const width = Math.round(boxWidth)
      const height = Math.round(boxHeight)

      return { x, y, width, height }
    },

    // 获取当前选中结果的图像尺寸
    getCurrentImageDimensions() {
      const img = this.$refs.resultDetailsImage
      if (img && img.complete && img.naturalWidth > 0) {
        return {
          width: img.naturalWidth,
          height: img.naturalHeight
        }
      }
      return { width: 0, height: 0 }
    },

    // 获取像素坐标（用于模板显示）
    getPixelCoords(boundingBox) {
      const dimensions = this.getCurrentImageDimensions()
      if (dimensions.width === 0 || dimensions.height === 0) {
        // 如果图像尺寸不可用，返回YOLO坐标的近似像素值
        return {
          x: Math.round((boundingBox.center_x - boundingBox.width / 2) * 1000),
          y: Math.round((boundingBox.center_y - boundingBox.height / 2) * 1000),
          width: Math.round(boundingBox.width * 1000),
          height: Math.round(boundingBox.height * 1000)
        }
      }
      return this.convertYoloToPixelCoords(boundingBox, dimensions.width, dimensions.height)
    },

    // 获取缓存的分页结果
    getCachedPaginatedResults() {
      const start = (this.currentResultPage - 1) * this.resultsPerPage
      const end = start + this.resultsPerPage
      const cacheKey = `${this.currentResultPage}-${this.resultsPerPage}-${this.detectionResults.length}`

      if (this.paginationCache && this.paginationCache.key === cacheKey) {
        return this.paginationCache.data
      }

      const result = this.detectionResults.slice(start, end)
      this.paginationCache = { key: cacheKey, data: result }
      return result
    },

    // 清理所有缓存
    clearAllCaches() {
      this.paginationCache = null
      if (this.categoriesCache) {
        this.categoriesCache.clear()
      }
      if (this.timestampCache) {
        this.timestampCache.clear()
      }
    },

    // 清理分页缓存
    clearPaginationCache() {
      this.paginationCache = null
    },

    // 清理媒体资源
    cleanupMediaResources() {
      // 使用媒体管理器清理所有资源
      if (this.mediaManager) {
        this.mediaManager.cleanup()
      }

      // 清理组件内的媒体引用
      this.selectedImage = null
      this.selectedImageFile = null
      this.selectedVideo = null
      this.selectedVideoFile = null
      this.rtspUrl = null
      this.previewsrclist = []

      // 清理上传和预处理结果路径
      this.uploadedFilePath = null
      this.uploadedFilePaths = []
      this.preprocessedFilePath = null
      this.preprocessedFilePaths = []
    },

    // Pagination methods
    handlePageSizeChange(newSize) {
      console.log(`页面大小改变: ${this.resultsPerPage} -> ${newSize}`)
      this.resultsPerPage = newSize
      this.currentResultPage = 1
      this.clearPaginationCache()
    },

    handleCurrentPageChange(newPage) {
      console.log(`页面切换: ${this.currentResultPage} -> ${newPage}`)
      this.currentResultPage = newPage
      this.clearPaginationCache()

    },

    // Batch operations

    deleteSelectedResults() {
      this.$confirm('确定要删除选中的检测结果吗？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.detectionResults = this.detectionResults.filter(result => !result.selected)
        this.allResultsSelected = false
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    clearAllResults() {
      this.$confirm('确定要清空所有检测结果吗？', '确认清空', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.detectionResults = []
        this.allResultsSelected = false
        this.$message.success('清空成功')
      }).catch(() => {
        this.$message.info('已取消清空')
      })
    },

    // 处理显示模式变化
    onDisplayModeChange() {
      if (this.resultDisplayMode === 'single') {
        this.$message.info('已切换到单次结果模式：每次检测将清空之前的结果')
      } else {
        this.$message.info('已切换到累计结果模式：检测结果将累积显示')
      }
      console.log(`显示模式已切换为: ${this.resultDisplayMode}`)
    },
    // 保存检测结果
    async batchSaveResults() {
      const selectedResults = this.detectionResults.filter(result => result.selected)
      if (selectedResults.length === 0) {
        this.$message.warning('请先选择要保存的检测结果')
        return
      }

      try {
        this.savingResults = true

        let totalSuccessCount = 0
        let totalFailCount = 0
        let allCreatedCategories = []

        // 为每个选中的结果进行保存
        for (const result of selectedResults) {
          try {
            // 获取该结果中的唯一类别
            const detectionCategories = this.getUniqueCategories(result.detections)

            if (detectionCategories.length === 0) {
              console.warn(`结果 ${result.id} 没有有效的类别信息`)
              totalFailCount++
              continue
            }

            let resultSuccessCount = 0

            // 为每个类别保存样本
            for (const category of detectionCategories) {
              try {
                // 检查类别是否存在，不存在则创建
                const categoryInfo = await this.ensureCategoryExists(category)

                // 保存该类别的检测结果
                await this.saveResultToSampleLibrary(result, categoryInfo, category)
                resultSuccessCount++

                if (categoryInfo.isNew && !allCreatedCategories.includes(category)) {
                  allCreatedCategories.push(category)
                }
              } catch (error) {
                console.error(`保存结果 ${result.id} 的类别 ${category} 失败:`, error)
              }
            }

            if (resultSuccessCount > 0) {
              totalSuccessCount++
            } else {
              totalFailCount++
            }

          } catch (error) {
            console.error(`处理结果 ${result.id} 失败:`, error)
            totalFailCount++
          }
        }

        // 更新样本库联动
        await this.updateSampleLibrarySync()

        // 提供用户反馈
        let message = `批量保存完成：成功 ${totalSuccessCount} 个结果`
        if (totalFailCount > 0) {
          message += `，失败 ${totalFailCount} 个`
        }
        if (allCreatedCategories.length > 0) {
          message += `，新创建类别: ${allCreatedCategories.join(', ')}`
        }

        if (totalSuccessCount > 0) {
          this.$message.success(message)
        } else {
          this.$message.error('批量保存失败，请重试')
        }

      } catch (error) {
        console.error('批量保存失败:', error)
        this.$message.error('保存过程中发生错误')
      } finally {
        this.savingResults = false
      }
    },

    // Individual result operations
    editResult(result) {
      console.log('开始编辑检测结果:', result)

      this.modifyingResult = result
      this.modifyDetections = JSON.parse(JSON.stringify(result.detections)) // 深拷贝
      this.selectedDetection = null
      this.showModifyDialog = true

      // 等待对话框渲染完成后初始化canvas
      this.$nextTick(() => {
        this.initializeModifyCanvas()
      })
    },

    async saveResult(result) {
      try {
        this.savingResults = true

        // 修改：自动根据检测结果的类别进行保存
        const detectionCategories = this.getUniqueCategories(result.detections)

        if (detectionCategories.length === 0) {
          this.$message.warning('检测结果中没有有效的类别信息')
          return
        }

        let savedCount = 0
        let createdCategories = []

        // 为每个检测到的类别保存样本
        for (const category of detectionCategories) {
          try {
            // 检查类别是否存在，不存在则创建
            const categoryInfo = await this.ensureCategoryExists(category)

            // 保存该类别的检测结果
            await this.saveResultToSampleLibrary(result, categoryInfo, category)
            savedCount++

            if (categoryInfo.isNew) {
              createdCategories.push(category)
            }
          } catch (error) {
            console.error(`保存类别 ${category} 失败:`, error)
          }
        }

        // 更新样本库联动
        await this.updateSampleLibrarySync()

        // 提供用户反馈
        let message = `成功保存 ${savedCount} 个类别的检测结果`
        if (createdCategories.length > 0) {
          message += `，新创建类别: ${createdCategories.join(', ')}`
        }
        this.$message.success(message)

      } catch (error) {
        console.error('保存结果失败:', error)
        this.$message.error('保存失败，请重试')
      } finally {
        this.savingResults = false
      }
    },

    // 显示类别选择对话框
    async showCategorySelectionDialog() {
      try {
        // 获取类别树
        await this.loadCategoryTree()

        return new Promise((resolve) => {
          this.$confirm('请选择要保存到的类别', '选择类别', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info',
            customClass: 'category-selection-dialog',
            message: this.renderCategorySelection()
          }).then(() => {
            resolve(true)
          }).catch(() => {
            this.selectedCategory = null
            resolve(false)
          })
        })
      } catch (error) {
        console.error('加载类别树失败:', error)
        this.$message.error('加载类别失败')
        throw error
      }
    },

    // 加载类别树
    async loadCategoryTree() {
      try {
        const response = await APIGetCategoryTree()
        if (response && response.status === 'success') {
          this.categoryTree = response.data || []
          console.log('类别树加载成功:', this.categoryTree)
        } else {
          throw new Error(response?.message || '获取类别树失败')
        }
      } catch (error) {
        console.error('获取类别树失败:', error)
        throw error
      }
    },

    // 渲染类别选择界面
    renderCategorySelection() {

      // 在实际实现中，这里应该是一个更复杂的类别选择界面
      const leafCategories = this.getLeafCategories(this.categoryTree)
      if (leafCategories.length > 0) {
        this.selectedCategory = leafCategories[0]
        return `将保存到类别: ${this.selectedCategory.label}`
      } else {
        return '没有可用的类别，请先在样本管理中创建类别'
      }
    },

    // 获取叶子节点类别
    getLeafCategories(categories) {
      const leafCategories = []

      function traverse(nodes) {
        for (const node of nodes) {
          if (!node.children || node.children.length === 0) {
            leafCategories.push(node)
          } else {
            traverse(node.children)
          }
        }
      }

      traverse(categories)
      return leafCategories
    },

    // 保存结果到样本库
    async saveResultToSampleLibrary(result, category, targetCategory = null) {
      try {
        // 创建FormData来上传图像和标注数据
        const formData = new FormData()

        // 添加类别路径
        formData.append('category_path', category.id)
        formData.append('category_name', targetCategory || category.label || category.name)

        // 获取图像文件
        const imageResponse = await fetch(`${serverAddress}${result.origin_image}`)
        const imageBlob = await imageResponse.blob()
        const imageFile = new File([imageBlob], `detection_${result.id}_${targetCategory || 'unknown'}.jpg`, { type: 'image/jpeg' })
        formData.append('files', imageFile)

        // 创建YOLO格式的标注文件（只包含指定类别的检测结果）
        const filteredDetections = targetCategory
          ? result.detections.filter(detection => detection.category === targetCategory)
          : result.detections

        const annotationContent = this.createYOLOAnnotation(filteredDetections)
        const annotationBlob = new Blob([annotationContent], { type: 'text/plain' })
        const annotationFile = new File([annotationBlob], `detection_${result.id}_${targetCategory || 'unknown'}.txt`, { type: 'text/plain' })
        formData.append('files', annotationFile)

        // 添加元数据
        formData.append('metadata', JSON.stringify({
          original_result_id: result.id,
          detection_timestamp: result.timestamp,
          target_category: targetCategory,
          detection_count: filteredDetections.length,
          confidence_avg: filteredDetections.reduce((sum, d) => sum + d.confidence, 0) / filteredDetections.length
        }))

        // 调用样本添加API
        const response = await APIAddSamples(formData)

        if (response && response.status === 'success') {
          console.log(`检测结果已保存到样本库: ${result.id}, 类别: ${targetCategory}`)
          return response
        } else {
          throw new Error(response?.message || '保存到样本库失败')
        }
      } catch (error) {
        console.error('保存到样本库失败:', error)
        throw error
      }
    },

    // 创建YOLO格式标注
    createYOLOAnnotation(detections) {
      const lines = []

      // 类别映射（需要与后端保持一致）
      const categoryMap = {
        '武装人员': 0,
        '运输车': 1,
        '突击车': 2,
        '82速射迫击炮': 3,
        '轮式突击炮': 4,
        '防坦克三角锥': 5,
        '坦克': 6,
        '地堡': 7,
        '步兵战车': 8,
        '榴弹炮': 9,
        '伪装网': 10,
        '无人装备': 11
      }

      for (const detection of detections) {
        const classId = categoryMap[detection.category] || 0
        const { center_x, center_y, width, height } = detection.boundingBox
        const confidence = detection.confidence / 100 // 转换为0-1范围

        // YOLO格式: class_id center_x center_y width height confidence
        lines.push(`${classId} ${center_x} ${center_y} ${width} ${height} ${confidence}`)
      }

      return lines.join('\n')
    },

    // 获取检测结果中的唯一类别
    getUniqueCategories(detections) {
      if (!detections || !Array.isArray(detections)) {
        return []
      }

      const categories = detections
        .map(detection => detection.category)
        .filter(category => category && category.trim())

      return [...new Set(categories)]
    },

    // 确保类别存在，不存在则创建
    async ensureCategoryExists(categoryName) {
      try {
        // 首先检查类别是否已存在
        await this.loadCategoryTree()

        const existingCategory = this.findCategoryByName(this.categoryTree, categoryName)
        if (existingCategory) {
          return { ...existingCategory, isNew: false }
        }

        // 类别不存在，创建新类别
        console.log(`创建新类别: ${categoryName}`)
        const newCategory = await this.createNewCategory(categoryName)

        // 重新加载类别树以获取最新数据
        await this.loadCategoryTree()

        return { ...newCategory, isNew: true }
      } catch (error) {
        console.error('确保类别存在失败:', error)
        throw error
      }
    },

    // 在类别树中查找指定名称的类别
    findCategoryByName(categories, name) {
      for (const category of categories) {
        if (category.label === name || category.name === name) {
          return category
        }
        if (category.children && category.children.length > 0) {
          const found = this.findCategoryByName(category.children, name)
          if (found) return found
        }
      }
      return null
    },

    // 创建新类别
    async createNewCategory(categoryName) {
      try {
        // 修复：使用样本库的API来创建类别
        // 首先获取当前的类别树
        await this.loadCategoryTree()

        // 创建新的类别节点
        const newCategoryId = Date.now()
        const newCategory = {
          id: newCategoryId,
          label: categoryName,
          name: categoryName,
          children: []
        }

        // 将新类别添加到类别树
        const updatedTree = [...this.categoryTree, newCategory]

        // 调用保存类别树的API
        const response = await APISaveCategoryTree({ tree_data: updatedTree })

        if (response && response.status === 'success') {
          console.log(`新类别创建成功: ${categoryName}`)
          // 更新本地类别树
          this.categoryTree = updatedTree
          return {
            id: newCategoryId,
            label: categoryName,
            name: categoryName
          }
        } else {
          throw new Error(response?.message || '创建类别失败')
        }
      } catch (error) {
        console.error('创建新类别失败:', error)
        // 如果API调用失败，返回一个临时的类别对象
        const tempId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
        console.log(`创建临时类别: ${categoryName}, ID: ${tempId}`)
        return {
          id: tempId,
          label: categoryName,
          name: categoryName,
          isTemporary: true
        }
      }
    },

    // 更新样本库联动
    async updateSampleLibrarySync() {
      try {
        // 触发样本库数据刷新
        // 这里可以发送事件或调用样本库的刷新方法
        console.log('触发样本库数据同步更新')

        // 如果有全局事件总线，可以发送更新事件
        if (this.$bus) {
          this.$bus.emit('sample-library-updated')
        }

        // 或者直接调用样本库的刷新API
        await this.refreshSampleLibrary()
      } catch (error) {
        console.error('样本库联动更新失败:', error)
      }
    },

    // 刷新样本库数据
    async refreshSampleLibrary() {
      try {
        const response = await fetch(`${serverAddress}api/detected/refresh_sample_library`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        })

        if (response.ok) {
          const result = await response.json()
          if (result.status === 'success') {
            console.log('样本库数据刷新成功')

            // 发送事件通知样本库界面刷新
            if (window.parent && window.parent !== window) {
              window.parent.postMessage({
                type: 'SAMPLE_LIBRARY_UPDATED',
                source: 'detection_interface'
              }, '*')
            }
          }
        }
      } catch (error) {
        console.log('样本库刷新API不可用，跳过刷新')
      }
    },

    // Manual correction methods
    initializeModifyCanvas() {
      const canvas = this.$refs.modifyCanvas
      const container = this.$refs.modifyCanvasContainer

      if (!canvas || !container) {
        console.error('修改对话框canvas或容器未找到')
        return
      }

      this.modifyCanvas = canvas
      this.modifyContext = canvas.getContext('2d')

      // 设置canvas尺寸
      const containerRect = container.getBoundingClientRect()
      canvas.width = containerRect.width - 20 // 留出边距
      canvas.height = containerRect.height - 20

      // 加载图像
      this.loadModifyImage()
    },

    loadModifyImage() {
      if (!this.modifyingResult) return

      const img = new Image()
      img.onload = () => {
        this.modifyImage = img
        this.drawModifyCanvas()
      }
      img.onerror = () => {
        this.$message.error('加载图像失败')
      }
      img.src = `${serverAddress}${this.modifyingResult.origin_image}`
    },

    drawModifyCanvas() {
      if (!this.modifyCanvas || !this.modifyContext || !this.modifyImage) return

      const ctx = this.modifyContext
      const canvas = this.modifyCanvas
      const img = this.modifyImage

      // 清除画布
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // 计算图像显示区域（保持宽高比）
      const imgAspect = img.width / img.height
      const canvasAspect = canvas.width / canvas.height

      let displayWidth, displayHeight, offsetX = 0, offsetY = 0

      if (imgAspect > canvasAspect) {
        displayWidth = canvas.width
        displayHeight = displayWidth / imgAspect
        offsetY = (canvas.height - displayHeight) / 2
      } else {
        displayHeight = canvas.height
        displayWidth = displayHeight * imgAspect
        offsetX = (canvas.width - displayWidth) / 2
      }

      // 绘制图像
      ctx.drawImage(img, offsetX, offsetY, displayWidth, displayHeight)

      // 绘制检测框
      this.drawModifyDetections(displayWidth, displayHeight, offsetX, offsetY)
    },

    drawModifyDetections(displayWidth, displayHeight, offsetX, offsetY) {
      const ctx = this.modifyContext

      this.modifyDetections.forEach((detection, index) => {
        const { boundingBox, category, confidence } = detection
        const colors = ['#ff0000', '#00ff00', '#0000ff']
        const color = colors[index % colors.length]

        // 转换YOLO坐标到canvas坐标
        const centerX = boundingBox.center_x * displayWidth + offsetX
        const centerY = boundingBox.center_y * displayHeight + offsetY
        const boxWidth = boundingBox.width * displayWidth
        const boxHeight = boundingBox.height * displayHeight

        const canvasX = centerX - boxWidth / 2
        const canvasY = centerY - boxHeight / 2

        // 绘制检测框
        ctx.strokeStyle = this.selectedDetection === detection ? '#ffffff' : color
        ctx.lineWidth = this.selectedDetection === detection ? 3 : 2
        ctx.strokeRect(canvasX, canvasY, boxWidth, boxHeight)

        // 绘制标签
        const labelText = `${category} ${confidence.toFixed(1)}%`
        ctx.font = '12px Arial'
        const textWidth = ctx.measureText(labelText).width

        // ctx.fillStyle = color
        // ctx.fillRect(canvasX, canvasY - 20, textWidth + 8, 20)

        ctx.fillStyle = '#ffffff'
        ctx.fillText(labelText, canvasX + 4, canvasY - 6)

        // 存储检测框的canvas坐标用于交互
        detection._canvasCoords = {
          x: canvasX,
          y: canvasY,
          width: boxWidth,
          height: boxHeight,
          displayWidth,
          displayHeight,
          offsetX,
          offsetY
        }
      })
    },

    // Canvas mouse interaction methods
    onCanvasMouseDown(event) {
      const rect = this.modifyCanvas.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top

      this.dragStartX = x
      this.dragStartY = y

      if (this.isCreatingBox) {
        // 开始创建新检测框
        this.newBoxStart = { x, y }
        this.newBoxEnd = { x, y }
      } else {
        // 检查是否点击了现有检测框
        const clickedDetection = this.getDetectionAtPoint(x, y)
        if (clickedDetection) {
          this.selectedDetection = clickedDetection
          this.isDragging = true
        } else {
          this.selectedDetection = null
        }
        this.drawModifyCanvas()
      }
    },

    onCanvasMouseMove(event) {
      if (!this.modifyCanvas) return

      const rect = this.modifyCanvas.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top

      if (this.isCreatingBox) {
        // 更新新检测框的结束位置
        this.newBoxEnd = { x, y }
        this.drawModifyCanvas()
        this.drawNewBox()
      } else if (this.isDragging && this.selectedDetection) {
        // 拖拽现有检测框
        const deltaX = x - this.dragStartX
        const deltaY = y - this.dragStartY

        this.moveDetection(this.selectedDetection, deltaX, deltaY)
        this.dragStartX = x
        this.dragStartY = y
        this.drawModifyCanvas()
      }
    },

    onCanvasMouseUp() {
      if (this.isCreatingBox) {
        // 完成新检测框创建
        this.finishNewBox()
        this.isCreatingBox = false
      }

      this.isDragging = false
    },

    onCanvasClick(event) {
      // 处理单击事件（选择检测框）
      if (!this.isDragging && !this.isCreatingBox) {
        const rect = this.modifyCanvas.getBoundingClientRect()
        const x = event.clientX - rect.left
        const y = event.clientY - rect.top

        const clickedDetection = this.getDetectionAtPoint(x, y)
        this.selectedDetection = clickedDetection
        this.drawModifyCanvas()
      }
    },

    getDetectionAtPoint(x, y) {
      for (const detection of this.modifyDetections) {
        if (detection._canvasCoords) {
          const coords = detection._canvasCoords
          if (x >= coords.x && x <= coords.x + coords.width &&
            y >= coords.y && y <= coords.y + coords.height) {
            return detection
          }
        }
      }
      return null
    },

    moveDetection(detection, deltaX, deltaY) {
      if (!detection._canvasCoords) return

      const coords = detection._canvasCoords
      const newCenterX = (coords.x + coords.width / 2 + deltaX - coords.offsetX) / coords.displayWidth
      const newCenterY = (coords.y + coords.height / 2 + deltaY - coords.offsetY) / coords.displayHeight

      // 确保检测框不超出图像边界
      const halfWidth = detection.boundingBox.width / 2
      const halfHeight = detection.boundingBox.height / 2

      detection.boundingBox.center_x = Math.max(halfWidth, Math.min(1 - halfWidth, newCenterX))
      detection.boundingBox.center_y = Math.max(halfHeight, Math.min(1 - halfHeight, newCenterY))
    },

    drawNewBox() {
      if (!this.modifyContext) return

      const ctx = this.modifyContext
      const start = this.newBoxStart
      const end = this.newBoxEnd

      const x = Math.min(start.x, end.x)
      const y = Math.min(start.y, end.y)
      const width = Math.abs(end.x - start.x)
      const height = Math.abs(end.y - start.y)

      ctx.strokeStyle = '#ff0000'
      ctx.lineWidth = 2
      ctx.setLineDash([5, 5])
      ctx.strokeRect(x, y, width, height)
      ctx.setLineDash([])
    },

    finishNewBox() {
      const start = this.newBoxStart
      const end = this.newBoxEnd

      const x = Math.min(start.x, end.x)
      const y = Math.min(start.y, end.y)
      const width = Math.abs(end.x - start.x)
      const height = Math.abs(end.y - start.y)

      // 检查框的大小是否合理
      if (width < 10 || height < 10) {
        this.$message.warning('检测框太小，请重新绘制')
        return
      }

      // 获取当前图像显示参数
      const detection = this.modifyDetections[0]
      if (!detection || !detection._canvasCoords) return

      const coords = detection._canvasCoords

      // 转换为YOLO格式坐标
      const centerX = (x + width / 2 - coords.offsetX) / coords.displayWidth
      const centerY = (y + height / 2 - coords.offsetY) / coords.displayHeight
      const normalizedWidth = width / coords.displayWidth
      const normalizedHeight = height / coords.displayHeight

      // 创建新检测结果
      const newDetection = {
        boundingBox: {
          center_x: Math.max(0, Math.min(1, centerX)),
          center_y: Math.max(0, Math.min(1, centerY)),
          width: Math.max(0, Math.min(1, normalizedWidth)),
          height: Math.max(0, Math.min(1, normalizedHeight))
        },
        category: this.availableCategories[0],
        confidence: 80.0
      }

      this.modifyDetections.push(newDetection)
      this.selectedDetection = newDetection
      this.drawModifyCanvas()

      this.$message.success('新检测框已添加')
    },

    // Dialog control methods
    addNewDetection() {
      this.isCreatingBox = true
      this.selectedDetection = null
      this.$message.info('请在图像上拖拽创建新的检测框')
    },

    deleteSelectedDetection() {
      if (!this.selectedDetection) return

      const index = this.modifyDetections.indexOf(this.selectedDetection)
      if (index > -1) {
        this.modifyDetections.splice(index, 1)
        this.selectedDetection = null
        this.drawModifyCanvas()
        this.$message.success('检测框已删除')
      }
    },

    selectDetection(detection) {
      this.selectedDetection = detection
      this.drawModifyCanvas()
    },

    removeDetection(index) {
      this.modifyDetections.splice(index, 1)
      if (this.selectedDetection === this.modifyDetections[index]) {
        this.selectedDetection = null
      }
      this.drawModifyCanvas()
      this.$message.success('检测框已删除')
    },

    addNewCategory() {
      if (!this.newCategoryName.trim()) {
        this.$message.warning('请输入类别名称')
        return
      }

      const categoryName = this.newCategoryName.trim()

      if (this.availableCategories.includes(categoryName)) {
        this.$message.warning('类别已存在')
        return
      }

      // 修复：创建临时类别对象，确保有正确的结构
      const tempCategory = {
        id: `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        label: categoryName,
        name: categoryName,
        isTemporary: true
      }

      // 添加到可用类别列表
      this.availableCategories.push(categoryName)

      // 如果当前有选中的检测结果，自动设置为新类别
      if (this.selectedDetection) {
        this.selectedDetection.category = categoryName
      }

      this.$message.success(`新类别 "${categoryName}" 已添加`)
      this.newCategoryName = ''

      console.log('新类别已添加:', tempCategory)
    },

    closeModifyDialog() {
      this.showModifyDialog = false
      this.modifyingResult = null
      this.modifyDetections = []
      this.selectedDetection = null
      this.modifyCanvas = null
      this.modifyContext = null
      this.modifyImage = null
      this.isDragging = false
      this.isCreatingBox = false
    },

    saveModifiedResult() {
      if (!this.modifyingResult) {
        console.warn('saveModifiedResult: modifyingResult为空')
        return
      }

      console.log('保存修改结果:', this.modifyingResult.id, '检测数量:', this.modifyDetections.length)

      // 更新原始结果的检测数据
      this.modifyingResult.detections = JSON.parse(JSON.stringify(this.modifyDetections))

      this.$message.success('检测结果修改已保存')
      this.closeModifyDialog()
    },

    // Video detection methods
    async detectCurrentVideoFrame() {
      const video = this.$refs.videoElement
      if (!video || video.paused || video.ended) {
        this.$message.warning('请先播放视频')
        return
      }

      try {
        this.isProcessing = true

        // 初始化帧提取Canvas
        this.initFrameCanvas()

        const canvas = this.videoRealTimeDetection.canvas
        const context = this.videoRealTimeDetection.context

        if (!canvas || !context) {
          throw new Error('Canvas初始化失败')
        }

        // 修复：处理跨域问题，使用Blob方式获取帧数据
        let frameData
        try {
          // 先尝试直接绘制和导出
          context.drawImage(video, 0, 0, canvas.width, canvas.height)
          frameData = canvas.toDataURL('image/jpeg', 0.8)
        } catch (error) {
          if (error.message.includes('Tainted canvases')) {
            console.log('检测到跨域问题')
            
          }
          throw error
        }

        const currentTime = video.currentTime
        console.log(`检测当前视频帧: 时间=${currentTime.toFixed(2)}s`)

        // 发送帧数据进行检测
        const response = await fetch(`${serverAddress}api/detect_video_frame`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            frame_data: frameData,
            timestamp: currentTime,
            image_type: this.selectedImageType
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()

        if (result.status === 'success') {
          // 处理检测结果 - 转换为标准格式
          const detectionResult = {
            id: crypto.randomUUID(),
            selected: false,
            image: result.data.image,
            detections: result.data.detections || [],
            timestamp: result.data.timestamp || Date.now(),
            frameTime: currentTime
          }

          // 根据显示模式处理结果
          if (this.resultDisplayMode === 'single') {
            this.detectionResults = [detectionResult]
          } else {
            this.detectionResults = [detectionResult, ...this.detectionResults]
          }

          this.$message.success(`当前帧检测完成，检测到 ${detectionResult.detections.length} 个目标`)
        } else {
          throw new Error(result.message || '检测失败')
        }

      } catch (error) {
        console.error('视频帧检测失败:', error)
        this.$message.error('视频帧检测失败: ' + error.message)
      } finally {
        this.isProcessing = false
      }
    },

    toggleVideoRealTimeDetection() {
      if (this.videoRealTimeDetection.isActive) {
        this.stopVideoRealTimeDetection()
      } else {
        this.startVideoRealTimeDetection()
      }
    },

    // 监听视频时间轴变化，支持实时同步
    onVideoTimeUpdate() {
      if (this.selectedInputType === 'video' && this.videoRealTimeDetection.isActive) {
        // 当用户拖拽时间轴时，可以触发当前帧的检测
        // 这里可以添加防抖逻辑，避免频繁检测
        const video = this.$refs.videoElement
        if (video && !video.paused) {
          // 可以在这里添加实时同步逻辑
          console.log(`视频时间更新: ${video.currentTime.toFixed(2)}s`)
        }
      }
    },

    updateDetectionCanvas() {
      const canvas = this.$refs.detectionCanvas
      if (!canvas) return

      const ctx = canvas.getContext('2d')
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Draw placeholder content
      ctx.fillStyle = '#f0f0f0'
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      ctx.fillStyle = '#666'
      ctx.font = '12px Arial'
      ctx.textAlign = 'center'
      ctx.fillText('检测结果预览', canvas.width / 2, canvas.height / 2)

      if (this.showDetectionBoxes) {
        // Draw sample detection boxes
        ctx.strokeStyle = '#ff0000'
        ctx.lineWidth = 2
        ctx.strokeRect(20, 20, 60, 40)
        ctx.strokeRect(120, 80, 50, 30)
      }
    },

    updatePreprocessCanvas() {
      const canvas = this.$refs.preprocessCanvas
      if (!canvas) return

      const ctx = canvas.getContext('2d')
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Draw placeholder content
      ctx.fillStyle = '#f8f8f8'
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      ctx.fillStyle = '#666'
      ctx.font = '12px Arial'
      ctx.textAlign = 'center'
      ctx.fillText('预处理结果', canvas.width / 2, canvas.height / 2)

      // Draw method-specific preview
      ctx.font = '10px Arial'
      ctx.fillText(`方法: ${this.selectedPreprocessMethod}`, canvas.width / 2, canvas.height / 2 + 20)
    },

    getImageUrl(imagePath) {
      // 处理图像URL
      if (!imagePath) return ''

      // 如果已经是完整URL，直接返回
      if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {

        return imagePath
      }

      // 如果以/开头，添加服务器地址
      if (imagePath.startsWith('/')) {
        return `${serverAddress.replace(/\/$/, '')}${imagePath}`
      }

      // 如果是相对路径（如 workspace/detected/...），添加服务器地址和前缀斜杠
      if (imagePath.startsWith('workspace/')) {
        return `${serverAddress.replace(/\/$/, '')}/${imagePath}`
      }

      // 其他情况直接返回
      return imagePath
    },

    // 获取当前图像的类型
    getCurrentImageType() {
      if (this.selectedInputType === 'image') {
        // 单图像模式
        if (this.fileManager.uploadedFiles && this.fileManager.uploadedFiles.length > 0) {
          return this.fileManager.uploadedFiles[0].image_type
        }
      } else if (this.selectedInputType === 'folder') {
        // 文件夹模式
        if (this.fileManager.uploadedFiles && this.fileManager.uploadedFiles.length > 0 &&
          this.currentImageIndex >= 0 && this.currentImageIndex < this.fileManager.uploadedFiles.length) {
          return this.fileManager.uploadedFiles[this.currentImageIndex].image_type
        }
      } else if (this.selectedInputType === 'video') {
        // 视频模式
        if (this.fileManager.uploadedFiles && this.fileManager.uploadedFiles.length > 0) {
          return this.fileManager.uploadedFiles[0].image_type
        }
      }
      return null
    },

    onImageLoad(result) {
      // 图像加载成功后绘制检测框
      console.log(`表格图像加载成功: ${result.image}`)
    },

    onImageError(result) {
      // 图像加载失败处理
      const originalPath = result.image
      const processedUrl = this.getImageUrl(originalPath)

      console.error(`表格图像加载失败:`)
      console.error(`  原始路径: ${originalPath}`)
      console.error(`  处理后URL: ${processedUrl}`)
      console.error(`  服务器地址: ${serverAddress}`)

      // 尝试测试URL是否可访问
      fetch(processedUrl, { method: 'HEAD' })
        .then(response => {
          console.error(`  URL测试结果: ${response.status} ${response.statusText}`)
        })
        .catch(error => {
          console.error(`  URL测试失败: ${error.message}`)
        })

      // 设置默认占位符图像
      const img = this.$refs[`result-image-${result.id}`]?.[0]
      if (img) {
        // 或者添加错误样式类
        img.classList.add('image-error')
      }
    },
  },

  beforeUnmount() {
    // 停止视频实时检测
    this.stopVideoRealTimeDetection()

    // 使用事件管理器统一清理所有资源
    if (this.eventManager) {
      this.eventManager.cleanup()
    }

    // 清理媒体资源（包含媒体管理器清理）
    this.cleanupMediaResources()

    // 清理缓存
    this.clearAllCaches()
  },

  mounted() {
    // 初始化事件管理器
    this.eventManager = new EventManager()

    // 初始化媒体资源管理器
    this.mediaManager = new MediaManager({
      maxCacheSize: 100 * 1024 * 1024, // 100MB缓存
      maxImageSize: 100 * 1024 * 1024,  // 100MB单个图片限制
      maxVideoSize: 1024 * 1024 * 1024, // 1G单个视频限制
      compressionQuality: 0.85         // 85%压缩质量
    })

    // Handle ResizeObserver errors
    this.handleResizeObserverError()

    
    
  },
  beforeDestroy() {
    this.webRtcServer.disconnect()
    this.webRtcServer = null
  },
  watch: {
    selectedPreprocessMethod() {
      this.updatePreprocessCanvas()
    },
    showDetectionBoxes() {
      this.updateDetectionCanvas()
    },
    detectionResults: {
      handler() {
        this.clearAllCaches()
      },
      deep: true
    }
  },
}
</script>

<style scoped>
@import '@/assets/css/variables.css';

.detected {
  overflow: hidden;
  background-size: cover;
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  background-image: url("../assets/imgs/backimg.png");
  display: flex;
  flex-direction: column;
}

/* Main Content Layout */
.main-content {
  flex: 1;
  display: grid;
  gap: 20px;
  padding: 20px;
  height: calc(100vh - 180px);
  overflow: hidden;
}

/* 默认布局：非实时模式 - 左右区域1:1比例 */
.main-content {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  grid-template-areas:
    "data-input detection-results"
    "operations detection-results";
}

/* 实时模式布局：恢复四象限布局 */
/*.main-content.realtime-layout {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  grid-template-areas:
    "data-input detection-results"
    "operations statistics-map";
}*/
.main-content.realtime-layout {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  grid-template-areas:
    "data-input detection-results"
    "operations detection-results";
}

/* 区域定位 */
.data-input-section {
  grid-area: data-input;
}

.detection-results-section {
  grid-area: detection-results;
}

.operations-section {
  grid-area: operations;
}

.statistics-map-section {
  grid-area: statistics-map;
}

/* Section Base Styles */
.section {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 0;
  border: 2px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: var(--transition-normal);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-md);
}

.section:hover {
  box-shadow: var(--shadow-primary);
  transform: translateY(-2px);
}

/* Section Headers */
.section-header {
  background: rgba(64, 158, 255, 0.15);
  color: var(--text-primary);
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(64, 158, 255, 0.25);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  position: relative;
  transition: all 0.3s ease;
}

.section-header:hover {
  background: rgba(64, 158, 255, 0.2);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-1px);
}

.section-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1) 0%, rgba(64, 158, 255, 0.05) 100%);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  z-index: -1;
}

/* Dark theme adaptations */
@media (prefers-color-scheme: dark) {
  .section-header {
    background: rgba(64, 158, 255, 0.2);
    border-bottom: 1px solid rgba(64, 158, 255, 0.3);
  }

  .section-title {
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
  }

  .section-title .icon {
    filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.7));
  }

  .action-btn {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(64, 158, 255, 0.4);
    color: #ffffff;
  }

  .action-btn:hover {
    background: rgba(0, 0, 0, 0.5);
    border-color: rgba(64, 158, 255, 0.6);
  }

  .results-table {
    --el-table-bg-color: rgba(0, 0, 0, 0.3);
    background: rgba(0, 0, 0, 0.3);
  }

  .results-table .el-table__body td {
    background: rgba(0, 0, 0, 0.2) !important;
  }

  .results-table .el-table__body tr:hover td {
    background: rgba(64, 158, 255, 0.2) !important;
  }
}

/* Header Controls for Data Input Section */
.header-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.section-title {
  margin: 0;
  font-size: 14px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  z-index: 1;
  position: relative;
}

.section-title .icon {
  font-size: 16px;
  filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.5));
}

.section-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(64, 158, 255, 0.3);
  color: #1a1a1a;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
  backdrop-filter: blur(5px);
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(64, 158, 255, 0.5);
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

/* Section Content */
.section-content {
  flex: 1;
  padding: 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: var(--bg-secondary);
}

/* 1. Data Input Section Styles */
.input-type-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-label {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 13px;
  white-space: nowrap;
}

.type-select {
  min-width: 110px;
  width: 110px;
}

.option-content {
  display: flex;
  align-items: center;
  gap: 6px;
}

.option-icon {
  font-size: 14px;
}

.upload-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.upload-group {
  display: flex;
  align-items: center;
  gap: 6px;
}

.upload-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  padding: 6px 12px;
}

.btn-icon {
  font-size: 14px;
}

.file-status {
  color: var(--success-color);
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
}

/* Full Preview Area */
.full-preview-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.full-preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
  overflow: hidden;
  min-height: 300px;
}

.preview-image-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  background: var(--bg-secondary);
}

.full-preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: var(--transition-fast);
}

.preview-image-wrapper:hover .full-preview-image {
  transform: scale(1.02);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-fast);
}

.preview-image-wrapper:hover .image-overlay {
  opacity: 1;
}

.overlay-content {
  text-align: center;
  color: var(--text-primary);
}

.zoom-icon {
  font-size: 32px;
  display: block;
  margin-bottom: 8px;
}

.image-actions-bar,
.folder-navigation-bar,
.video-actions-bar,
.stream-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin: 0;
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-secondary);
}

.nav-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.folder-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.image-counter {
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 600;
}

.folder-name {
  color: var(--text-secondary);
  font-size: 11px;
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.file-name {
  color: var(--text-primary);
  font-size: 12px;
  font-weight: 500;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  color: var(--text-secondary);
  font-size: 10px;
}

.folder-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.image-counter {
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 600;
}

.folder-name {
  color: var(--text-secondary);
  font-size: 11px;
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.file-name {
  color: var(--text-primary);
  font-size: 12px;
  font-weight: 500;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  color: var(--text-secondary);
  font-size: 10px;
}

.video-preview-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  padding: 16px;
  overflow: hidden;
  min-height: 200px;
}

.full-preview-video {
  width: 100%;
  height: auto;
  max-width: 100%;
  max-height: calc(100% - 20px);
  border-radius: var(--radius-sm);
  background: #000;
  object-fit: contain;
}

/* 确保视频控制条可见 */
.full-preview-video::-webkit-media-controls-panel {
  display: flex !important;
  opacity: 1 !important;
}

.full-preview-video::-webkit-media-controls {
  width: 100% !important;
  height: auto !important;
}

/* 兼容Firefox */
.full-preview-video::-moz-media-controls {
  display: block !important;
}

.video-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.video-name {
  color: var(--text-primary);
  font-size: 12px;
  font-weight: 500;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-size {
  color: var(--text-secondary);
  font-size: 10px;
}

/* Video Loading and Error States */
.video-loading,
.video-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: var(--text-primary);
  background: rgba(0, 0, 0, 0.8);
  padding: 20px;
  border-radius: var(--radius-md);
  z-index: 10;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-secondary);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error-icon {
  font-size: 32px;
  color: var(--danger-color);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 响应式视频处理 */
@media (max-width: 768px) {
  .video-preview-wrapper {
    padding: 12px;
    min-height: 180px;
  }

  .full-preview-video {
    max-height: calc(100% - 15px);
  }
}

@media (max-width: 480px) {
  .video-preview-wrapper {
    padding: 8px;
    min-height: 150px;
  }

  .video-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .video-name {
    max-width: 150px;
    font-size: 11px;
  }
}

.stream-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #dc3545;
  transition: var(--transition-fast);
}

.status-dot.active {
  background: #28a745;
  animation: pulse 2s infinite;
}

.status-text {
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
}

/* Empty State */
.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  border: 2px dashed var(--border-secondary);
}

.empty-content {
  text-align: center;
  color: var(--text-muted);
}

.empty-icon {
  font-size: 64px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.empty-description {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
  }
}

/* 2. Detection Results Section Styles */
.results-table-container {
  flex: 1;
  overflow: hidden;
  margin: 0;
  padding: 0;
  border-radius: var(--radius-md);
}

.results-table {
  width: 100%;
  height: 100%;
  /* 更贴合背景色的设计 */
  --table-header-bg: rgba(30, 35, 45, 0.95);
  --table-cell-bg: rgba(20, 25, 35, 0.8);
  --table-row-bg: rgba(25, 30, 40, 0.85);
  --table-hover-bg: rgba(64, 158, 255, 0.15);
  --table-border-color: rgba(64, 158, 255, 0.2);

  background: var(--table-cell-bg);
  border-radius: var(--radius-md);
  overflow: hidden;
  contain: layout style paint;
  will-change: auto;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  table-layout: fixed;
}

.results-table :deep(.el-table__header-wrapper) {
  position: sticky;
  top: 0;
  z-index: 100;
  background: var(--table-header-bg);
  backdrop-filter: blur(10px);
  border-bottom: 2px solid var(--table-border-color);
  contain: layout style paint;
  transform: translateZ(0);
}

.results-table :deep(.el-table__header th) {
  background: var(--table-header-bg) !important;
  color: #ffffff !important;
  font-weight: 700 !important;
  font-size: 13px !important;
  padding: 14px 12px !important;
  border-bottom: 2px solid var(--table-border-color) !important;
  border-right: 1px solid var(--table-border-color) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  will-change: auto;
  contain: layout style;
}

.results-table :deep(.el-table__body) {
  contain: layout style;
  transform: translateZ(0);
}

.results-table :deep(.el-table__body td) {
  background: var(--table-cell-bg) !important;
  color: #e8e8e8 !important;
  padding: 12px !important;
  border-bottom: 1px solid rgba(64, 158, 255, 0.1) !important;
  border-right: 1px solid rgba(64, 158, 255, 0.1) !important;
  vertical-align: middle;
  contain: layout style;
  will-change: auto;
}

.results-table :deep(.el-table__body tr) {
  contain: layout style;
  transform: translateZ(0);
}

.results-table :deep(.el-table__body tr:hover td) {
  background: var(--table-hover-bg) !important;
  color: #ffffff !important;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.results-table :deep(.el-table__body tr.el-table__row--striped td) {
  background: rgba(25, 30, 40, 0.9) !important;
}

.results-table :deep(.el-table__body tr.el-table__row--striped:hover td) {
  background: var(--table-hover-bg) !important;
}

.image-cell {
  position: relative;
}

.result-image-container {
  position: relative;
  display: inline-block;
}

.result-img {
  width: 150px;
  height: 100px;
  object-fit: cover;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-primary);
}

.annotation-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 150px;
  height: 100px;
  pointer-events: none;
  border-radius: var(--radius-sm);
}

.info-cell {
  min-width: 200px;
}

.result-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-label {
  font-weight: 600;
  color: var(--text-primary);
  min-width: 60px;
}

.info-value {
  color: var(--text-secondary);
}

.confidence {
  background: var(--success-color);
  color: var(--text-primary);
  padding: 2px 8px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 600;
}

.actions-cell {
  white-space: nowrap;
}

.correct-btn,
.save-btn {
  padding: 6px 12px;
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: 12px;
  margin-right: 8px;
  transition: var(--transition-fast);
}

.correct-btn {
  background: var(--warning-color);
  color: var(--text-primary);
}

.correct-btn:hover {
  background: var(--warning-dark);
}

.save-btn {
  background: var(--success-color);
  color: var(--text-primary);
}

.save-btn:hover {
  background: var(--success-dark);
}

/* 3. Operations Section Styles */
.operation-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.operation-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-operation-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
  white-space: nowrap;
}

.header-method-select {
  min-width: 80px;
  width: 80px;
}

.input-source-switch {
  margin-left: 12px;
}

.input-source-switch .el-switch__label {
  font-size: 12px;
  color: #606266;
}

.input-source-switch .el-switch__label.is-active {
  color: #409EFF;
}

/* 视频实时检测指示器 */
.realtime-detection-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  z-index: 10;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  background: #ff4444;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Results Preview Area */
.results-preview-area {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 16px;
  max-height: 100%;
  overflow: visible;
}

/* Processing Status Overlay */
.processing-status-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  justify-content: center;
  padding: 16px;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(2px);
  animation: fadeIn 0.3s ease-in-out;
}

.processing-status-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 16px 20px;
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-lg);
  min-width: 300px;
  max-width: 500px;
}

.status-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.status-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 15px;
}

.status-percentage {
  color: var(--primary-color);
  font-weight: 700;
  font-size: 16px;
}

.status-progress-bar {
  width: 100%;
  height: 8px;
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  overflow: hidden;
  border: 1px solid var(--border-primary);
}

.status-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  transition: width 0.3s ease;
  box-shadow: 0 0 10px rgba(77, 211, 255, 0.3);
}

/* Fade in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Preview Results Grid */
.preview-results-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  flex: 1;
}

.result-container {
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
  overflow: visible;
  display: flex;
  flex-direction: column;
  max-height: 100%;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-secondary);
}

.result-title {
  color: var(--text-primary);
  font-size: 12px;
  font-weight: 600;
}

.result-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-controls-small {
  display: flex;
  align-items: center;
  gap: 4px;
}

.nav-counter {
  color: var(--text-secondary);
  font-size: 11px;
  font-weight: 500;
  min-width: 40px;
  text-align: center;
}

.result-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background: var(--bg-secondary);
  overflow: visible;
}

/* Result component styles */
.image-result-container,
.video-result-container,
.canvas-result-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  max-height: 100%;
  overflow: visible;
}

.result-image,
.result-video,
.result-canvas {
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-sm);
  width: 300px;
  height: 200px;
  object-fit: contain;
}

.result-image:hover,
.result-video:hover,
.result-canvas:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-md);
  object-fit: contain;
}

.result-video {
  background: #000;
}

/* Details Dialog Styles */
.details-dialog {
  backdrop-filter: blur(10px);
}

.details-content {
  min-height: 500px;
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  padding: 20px;
}

/* Image Details */
.image-details-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.comparison-view {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  flex: 1;
  min-height: 400px;
}

.original-view,
.processed-view {
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
  overflow: hidden;
}

.view-title {
  margin: 0;
  padding: 12px 16px;
  background: var(--bg-tertiary);
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid var(--border-secondary);
}

.image-viewer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: var(--bg-secondary);
  position: relative;
}

.detail-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  user-select: none;
  transform-origin: center center;
}

.details-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding: 12px 16px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-secondary);
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.zoom-info {
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 500;
  min-width: 50px;
}

.details-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
}

.details-counter {
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 600;
  min-width: 60px;
  text-align: center;
}

/* Video Details */
.video-details-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-comparison-view {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  flex: 1;
  min-height: 400px;
}

.original-video-view,
.processed-video-view {
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
  overflow: hidden;
}

.detail-video {
  width: 100%;
  height: 100%;
  background: #000;
  object-fit: contain;
}

.video-details-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin-top: 16px;
  padding: 12px 16px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-secondary);
}

/* Detection Details */
.detection-details-container {
  height: 100%;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  min-height: 600px;
  max-height: 100%;
  padding: 20px;
  overflow: hidden;
}

.detection-details-left,
.detection-details-right {
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: hidden;
}

.detection-details-right {
  max-width: 500px;
  width: 500px;
}

.detection-comparison-view {
  display: flex;
  justify-content: center;
  flex: 1;
  min-height: 400px;
}

.overlapped-canvas-container {
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  flex-shrink: 0;
}

.canvas-viewer-stack {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background: var(--bg-secondary);
  min-height: 300px;
  height: 350px;
}

.canvas-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.original-layer {
  z-index: 1;
}

.result-layer {
  z-index: 2;
  pointer-events: none;
}

.result-canvas {
  pointer-events: auto;
}

.canvas-viewer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background: var(--bg-secondary);
}

.detail-canvas {
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.original-canvas {
  background: var(--bg-primary);
}

.result-canvas {
  background: transparent;
}

.detail-canvas:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-md);
}

.detection-details-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding: 12px 16px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-secondary);
}

/* Detection Info Panel */
.detection-info-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(20, 25, 35, 0.9);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(64, 158, 255, 0.3);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  max-width: 100%;
  min-width: 0;
  width: 100%;
}

.detection-info-header {
  padding: 16px 20px;
  background: rgba(30, 35, 45, 0.95);
  border-bottom: 1px solid rgba(64, 158, 255, 0.2);
  backdrop-filter: blur(5px);
}

.detection-summary {
  display: flex;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(64, 158, 255, 0.1);
  flex-wrap: wrap;
  min-width: 0;
}

.detection-status {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(64, 158, 255, 0.1);
  min-width: 0;
  overflow: hidden;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: var(--radius-md);
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-indicator.has-detections {
  background: rgba(40, 167, 69, 0.1);
  border-color: rgba(40, 167, 69, 0.3);
}

.status-icon {
  font-size: 16px;
  color: rgba(255, 193, 7, 0.8);
}

.status-indicator.has-detections .status-icon {
  color: rgba(40, 167, 69, 0.8);
}

.status-text {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.detection-options {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Detection Results Section Styles */
.detection-results-section {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
  overflow-y: auto;
  flex: 1;
}

.mode-switch-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mode-label {
  font-size: 12px;
  color: var(--text-primary);
  font-weight: 600;
}

.mode-switch {
  --el-switch-on-color: var(--primary-color);
  --el-switch-off-color: #dcdfe6;
}

.mode-switch .el-switch__label {
  color: rgba(64, 158, 255, 0.8) !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.mode-switch .el-switch__label.is-active {
  color: #409EFF !important;
  font-weight: 700 !important;
  text-shadow: 0 0 8px rgba(64, 158, 255, 0.5) !important;
}

/* Table Image Container */
.table-image-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px;
  cursor: pointer;
  transition: var(--transition-fast);
}

.table-image-container:hover {
  transform: scale(1.05);
}

.table-result-image {
  width: 160px;
  height: 100px;
  object-fit: cover;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-secondary);
  display: block;
  transition: var(--transition-fast);
}

.table-image-container:hover .table-result-image {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.table-result-canvas {
  position: absolute;
  top: 8px;
  left: 8px;
  width: 160px;
  height: 100px;
  pointer-events: none;
  border-radius: var(--radius-sm);
}

.table-result-image.image-error {
  background-color: #f5f5f5;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23999'%3E%3Cpath d='M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 24px 24px;
}

/* 全选复选框样式 */
.select-all-checkbox {
  --el-checkbox-checked-bg-color: var(--primary-color);
  --el-checkbox-checked-border-color: var(--primary-color);
}

.select-all-checkbox :deep(.el-checkbox__inner) {
  background-color: transparent;
  border-color: rgba(255, 255, 255, 0.6);
}

.select-all-checkbox :deep(.el-checkbox__inner:hover) {
  border-color: var(--primary-color);
}

/* Table Result Info */
.table-result-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 4px 0;
}

.info-stats {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 13px;
  justify-content: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  min-width: 40px;
}

.stat-label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  color: #4dd3ff;
  font-weight: 700;
  font-size: 16px;
  text-shadow: 0 0 8px rgba(77, 211, 255, 0.4);
}

.info-timestamp {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  font-family: 'Courier New', monospace;
  text-align: center;
  padding: 2px 0;
}

.info-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
}

.category-tag {
  --el-tag-bg-color: rgba(64, 158, 255, 0.2);
  --el-tag-border-color: rgba(64, 158, 255, 0.4);
  --el-tag-text-color: #ffffff;
  font-size: 10px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.category-tag:hover {
  --el-tag-bg-color: rgba(64, 158, 255, 0.3);
  --el-tag-border-color: rgba(64, 158, 255, 0.6);
}

/* Table Actions */
.table-actions {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  justify-content: center;
}

.table-actions .action-btn {
  --el-button-size: 28px;
  --el-button-font-size: 11px;
  padding: 0 10px;
  font-weight: 600;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.table-actions .action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Bottom Controls */
.results-bottom-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0 8px;
  border-top: 1px solid var(--border-secondary);
  margin-top: 16px;
}

.pagination-container {
  flex: 1;
}

.result-pagination {
  --el-pagination-font-size: 12px;
  --el-pagination-button-width: 28px;
  --el-pagination-button-height: 28px;
}

.batch-operations {
  display: flex;
  gap: 8px;
}

.batch-btn {
  --el-button-size: 28px;
  --el-button-font-size: 11px;
  --el-button-padding: 0 12px;
}

/* Result Details Dialog */
.result-details-dialog {
  --el-dialog-content-font-size: 14px;
  --el-dialog-padding-primary: 20px;
}

.result-details-dialog :deep(.el-dialog__body) {
  padding: 0;
  background: linear-gradient(135deg, rgba(15, 20, 30, 0.95), rgba(25, 35, 50, 0.95));
}

.result-details-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  min-height: 600px;
  padding: 20px;
}

.result-details-left,
.result-details-right {
  display: flex;
  flex-direction: column;
}

/* Details Image Section */
.details-image-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(20, 25, 35, 0.9);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(64, 158, 255, 0.3);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.details-image-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: rgba(30, 35, 45, 0.95);
  border-bottom: 1px solid rgba(64, 158, 255, 0.2);
  backdrop-filter: blur(5px);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.zoom-info {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(64, 158, 255, 0.1);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  border: 1px solid rgba(64, 158, 255, 0.3);
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-group {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.zoom-btn {
  --el-button-bg-color: transparent;
  --el-button-border-color: rgba(64, 158, 255, 0.3);
  --el-button-text-color: rgba(255, 255, 255, 0.8);
  --el-button-hover-bg-color: rgba(64, 158, 255, 0.2);
  --el-button-hover-border-color: rgba(64, 158, 255, 0.5);
  --el-button-hover-text-color: #ffffff;
}

.annotation-toggle-btn {
  --el-button-bg-color: rgba(64, 158, 255, 0.1);
  --el-button-border-color: rgba(64, 158, 255, 0.3);
  --el-button-text-color: rgba(255, 255, 255, 0.9);
  --el-button-hover-bg-color: rgba(64, 158, 255, 0.2);
  --el-button-hover-border-color: rgba(64, 158, 255, 0.5);
}

.details-section-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.details-controls {
  display: flex;
  gap: 8px;
}

.annotation-toggle-btn,
.zoom-reset-btn {
  --el-button-size: 28px;
  --el-button-font-size: 11px;
}

.details-image-viewer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: rgba(10, 15, 25, 0.8);
  overflow: hidden;
  position: relative;
  min-height: 400px;
}

.image-viewer-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.details-image {
  max-width: 700px;
  max-height: 500px;
  width: auto;
  height: auto;
  border-radius: var(--radius-sm);
  transform-origin: center center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(64, 158, 255, 0.2);
  display: block;
}

.details-canvas {
  position: absolute;
  top: 50%;
  left: 50%;
  transform-origin: center center;
  pointer-events: none;
  /* 负边距通过JavaScript动态设置，确保canvas中心与img中心对齐 */
}

.drag-hint {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(64, 158, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  border-radius: var(--radius-sm);
  border: 1px solid rgba(64, 158, 255, 0.4);
  font-size: 12px;
  backdrop-filter: blur(5px);
  animation: fadeInOut 2s infinite;
  pointer-events: none;
}

@keyframes fadeInOut {

  0%,
  100% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
  }
}

.details-navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 20px;
  background: rgba(30, 35, 45, 0.95);
  border-top: 1px solid rgba(64, 158, 255, 0.2);
  backdrop-filter: blur(5px);
}

.nav-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.nav-counter {
  display: flex;
  align-items: center;
  gap: 4px;
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.current-index {
  color: #4dd3ff;
  font-size: 16px;
  text-shadow: 0 0 8px rgba(77, 211, 255, 0.4);
}

.separator {
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
}

.total-count {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.nav-btn {
  --el-button-size: 32px;
  --el-button-font-size: 12px;
  --el-button-bg-color: rgba(64, 158, 255, 0.1);
  --el-button-border-color: rgba(64, 158, 255, 0.3);
  --el-button-text-color: rgba(255, 255, 255, 0.9);
  --el-button-hover-bg-color: rgba(64, 158, 255, 0.2);
  --el-button-hover-border-color: rgba(64, 158, 255, 0.5);
  --el-button-hover-text-color: #ffffff;
  --el-button-disabled-bg-color: rgba(255, 255, 255, 0.05);
  --el-button-disabled-border-color: rgba(255, 255, 255, 0.1);
  --el-button-disabled-text-color: rgba(255, 255, 255, 0.3);
  min-width: 90px;
  font-weight: 600;
}

.nav-counter {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-primary);
  min-width: 60px;
  text-align: center;
}

/* Details Info Section */
.details-info-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.details-info-header {
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-secondary);
}

.detection-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.summary-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: 16px;
  text-align: center;
}

.summary-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.summary-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color);
  font-family: var(--font-mono);
}

.category-statistics {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: 16px;
}

.subsection-title {
  margin: 0 0 12px 0;
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary);
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
}

.category-name {
  font-size: 12px;
  color: var(--text-primary);
  font-weight: 500;
}

.category-count {
  font-size: 12px;
  color: var(--primary-color);
  font-weight: 600;
  font-family: var(--font-mono);
}

.detection-details-list {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: 16px;
  flex: 1;
}

.details-table-container {
  margin-top: 12px;
  border-radius: var(--radius-md);
  overflow: hidden;
  border: 1px solid rgba(64, 158, 255, 0.2);
}

.detection-table {
  --el-table-font-size: 12px;
  --el-table-header-height: 36px;
  --el-table-row-height: 32px;
  --el-table-border-color: rgba(64, 158, 255, 0.2);
  background: transparent;
  contain: layout style;
  will-change: auto;
}

.detection-table :deep(.el-table__body tr:hover td) {
  background: rgba(64, 158, 255, 0.15) !important;
  color: #ffffff !important;
}

.detection-table :deep(.el-table__body tr.el-table__row--striped td) {
  background: rgba(25, 30, 40, 0.9) !important;
}

.detection-table :deep(.el-table__body tr.el-table__row--striped:hover td) {
  background: rgba(64, 158, 255, 0.15) !important;
}

/* 类别标签样式 */
.category-tag-small {
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
}

/* 边界框信息样式 */
.bbox-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-family: 'Courier New', monospace;
}

.bbox-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bbox-label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 10px;
  font-weight: 600;
  min-width: 12px;
}

.bbox-value {
  color: #4dd3ff;
  font-size: 11px;
  font-weight: 700;
  min-width: 30px;
  text-align: right;
}

/* 置信度显示样式 */
.confidence-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.confidence-bar {
  width: 60px;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.confidence-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff4757 0%, #ffa502 50%, #2ed573 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.confidence-text {
  color: #4dd3ff;
  font-size: 11px;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  text-shadow: 0 0 4px rgba(77, 211, 255, 0.4);
}

.bbox-text,
.confidence-text {
  font-family: var(--font-mono);
  font-size: 10px;
}

.confidence-text {
  color: var(--primary-color);
  font-weight: 600;
}

/* Real-time Status Panel */
.realtime-status-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  padding: 12px 16px;
  border: 1px solid var(--border-secondary);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-text {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 14px;
}

.realtime-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 11px;
}

.stat-value {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 14px;
}

/* 4. Statistics/Map Section Styles */
.charts-container {
  display: flex;
  gap: 16px;
  height: 100%;
}

.chart-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  padding: 16px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-primary);
}

.chart-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
}

.chart-placeholder {
  flex: 1;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-secondary);
}

.placeholder-content {
  text-align: center;
  color: var(--text-muted);
}

.chart-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 12px;
}

.mock-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
}

.mock-stats .stat-item {
  background: var(--bg-tertiary);
  padding: 8px 12px;
  border-radius: var(--radius-sm);
  font-size: 14px;
  color: var(--text-primary);
}

.mock-bars {
  display: flex;
  justify-content: space-around;
  align-items: end;
  height: 100px;
  margin-top: 16px;
  gap: 8px;
}

.bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.bar {
  width: 30px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: 2px 2px 0 0;
  min-height: 10px;
}

.bar-item span {
  font-size: 12px;
  text-align: center;
  color: var(--text-secondary);
}

.map-container {
  height: 100%;
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-primary);
}

/* Image Preview Modal */
.image-preview-modal {
  backdrop-filter: blur(10px);
}

.modal-image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 500px;
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  padding: 20px;
  position: relative;
}

.image-zoom-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  border-radius: var(--radius-sm);
  background: #000;
  min-height: 400px;
  width: 100%;
  position: relative;
}

.modal-preview-image {
  max-width: 90%;
  max-height: 60vh;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: var(--radius-sm);
  box-shadow: var(--shadow-md);
  user-select: none;
  transform-origin: center center;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 16px 0;
  padding: 8px 16px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-secondary);
}

.zoom-level {
  color: var(--text-primary);
  font-weight: 600;
  min-width: 50px;
  text-align: center;
  font-size: 14px;
}

.modal-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 16px;
  padding: 12px 16px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-secondary);
}

.nav-btn {
  min-width: 100px;
}

.image-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.image-counter {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 16px;
}

.image-name {
  color: var(--text-secondary);
  font-size: 12px;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 12px;
}

.footer-left,
.footer-right {
  display: flex;
  gap: 8px;
}

.footer-left {
  flex: 1;
}

.footer-right {
  justify-content: flex-end;
}

/* Modal responsive design */
@media (max-width: 768px) {
  .modal-image-container {
    padding: 12px;
    min-height: 300px;
  }

  .image-zoom-container {
    min-height: 250px;
  }

  .modal-preview-image {
    max-height: 50vh;
  }

  .zoom-controls {
    flex-wrap: wrap;
    gap: 8px;
    padding: 6px 12px;
  }

  .modal-navigation {
    flex-direction: column;
    gap: 12px;
    padding: 8px 12px;
  }

  .nav-btn {
    min-width: 80px;
    font-size: 12px;
  }

  .file-info,
  .video-info {
    align-items: center;
    text-align: center;
  }

  .file-name,
  .video-name {
    max-width: 150px;
  }

  .modal-footer {
    flex-direction: column;
    gap: 8px;
  }

  .footer-left,
  .footer-right {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .modal-image-container {
    padding: 8px;
    min-height: 250px;
  }

  .image-zoom-container {
    min-height: 200px;
  }

  .modal-preview-image {
    max-height: 40vh;
  }

  .zoom-controls {
    gap: 6px;
    padding: 4px 8px;
  }

  .zoom-level {
    font-size: 12px;
    min-width: 40px;
  }

  .image-counter {
    font-size: 14px;
  }

  .image-name {
    font-size: 11px;
  }
}

/* RTSP Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  border: 1px solid var(--border-primary);
}

.modal-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--text-primary);
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--transition-fast);
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 20px;
  background: var(--bg-secondary);
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.rtsp-input {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  font-size: 14px;
  box-sizing: border-box;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.rtsp-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(77, 211, 255, 0.1);
}

.modal-footer {
  padding: 16px 20px;
  background: var(--bg-tertiary);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid var(--border-secondary);
}

.connect-btn {
  background: var(--success-color);
  color: var(--text-primary);
  border: none;
  padding: 10px 20px;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-weight: 600;
  transition: var(--transition-fast);
}

.connect-btn:hover {
  background: var(--success-dark);
}

.cancel-btn {
  background: var(--bg-muted);
  color: var(--text-secondary);
  border: none;
  padding: 10px 20px;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-weight: 600;
  transition: var(--transition-fast);
}

.cancel-btn:hover {
  background: var(--bg-tertiary);
}

/* Enhanced RTSP Configuration Dialog */
.rtsp-config-dialog {
  --el-dialog-content-font-size: 14px;
}

.rtsp-config-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.rtsp-config-content {
  padding: 20px;
}

/* Connection Status Card */
.connection-status-card {
  background: linear-gradient(135deg, rgba(15, 20, 30, 0.9), rgba(25, 35, 50, 0.9));
  border-radius: var(--radius-lg);
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid rgba(64, 158, 255, 0.2);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #666;
  position: relative;
}

.status-dot.connecting {
  background: #ffa502;
  animation: pulse 1.5s infinite;
}

.status-dot.connected {
  background: #2ed573;
  box-shadow: 0 0 10px rgba(46, 213, 115, 0.5);
}

.status-dot.error {
  background: #ff4757;
  box-shadow: 0 0 10px rgba(255, 71, 87, 0.5);
}

.status-dot.disconnected {
  background: #666;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.status-text {
  color: #ffffff;
  font-weight: 600;
  font-size: 16px;
}

.connection-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

/* Configuration Form */
.config-form {
  margin-bottom: 24px;
}

.config-tabs :deep(.el-tabs__header) {
  margin-bottom: 20px;
}

.config-tabs :deep(.el-tabs__nav-wrap::after) {
  background-color: rgba(64, 158, 255, 0.2);
}

.config-tabs :deep(.el-tabs__active-bar) {
  background-color: var(--primary-color);
}

.config-tabs :deep(.el-tabs__item) {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 600;
}

.config-tabs :deep(.el-tabs__item.is-active) {
  color: var(--primary-color);
}

/* Quick Connect Form */
.quick-connect-form {
  padding: 20px 0;
}

.form-section {
  margin-bottom: 24px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 14px;
}

.rtsp-url-input {
  --el-input-bg-color: rgba(255, 255, 255, 0.1);
  --el-input-border-color: rgba(64, 158, 255, 0.3);
  --el-input-text-color: #ffffff;
  --el-input-placeholder-color: rgba(255, 255, 255, 0.5);
}

.rtsp-url-input :deep(.el-input__inner) {
  background: var(--el-input-bg-color);
  border-color: var(--el-input-border-color);
  color: var(--el-input-text-color);
}

.rtsp-url-input :deep(.el-input__inner):focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.rtsp-url-input :deep(.el-input-group__prepend) {
  background: rgba(64, 158, 255, 0.2);
  border-color: rgba(64, 158, 255, 0.3);
  color: var(--primary-color);
  font-weight: 600;
}

.input-hint {
  margin-top: 6px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.4;
}

/* Preset Section */
.preset-section {
  margin-top: 20px;
}

.preset-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.preset-btn {
  --el-button-bg-color: rgba(64, 158, 255, 0.1);
  --el-button-border-color: rgba(64, 158, 255, 0.3);
  --el-button-text-color: var(--primary-color);
  --el-button-hover-bg-color: rgba(64, 158, 255, 0.2);
  --el-button-hover-border-color: rgba(64, 158, 255, 0.5);
}

/* Advanced Form */
.advanced-form {
  padding: 20px 0;
}

.advanced-form .el-input,
.advanced-form .el-input-number {
  --el-input-bg-color: rgba(255, 255, 255, 0.1);
  --el-input-border-color: rgba(64, 158, 255, 0.3);
  --el-input-text-color: #ffffff;
}

.advanced-form .el-input :deep(.el-input__inner),
.advanced-form .el-input-number :deep(.el-input__inner) {
  background: var(--el-input-bg-color);
  border-color: var(--el-input-border-color);
  color: var(--el-input-text-color);
}

.options-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-top: 8px;
}

.options-grid .el-checkbox {
  --el-checkbox-text-color: rgba(255, 255, 255, 0.8);
  --el-checkbox-checked-bg-color: var(--primary-color);
  --el-checkbox-checked-border-color: var(--primary-color);
}

/* Connection Test */
.connection-test {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
  border-top: 1px solid rgba(64, 158, 255, 0.2);
}

.test-btn {
  --el-button-bg-color: rgba(64, 158, 255, 0.2);
  --el-button-border-color: rgba(64, 158, 255, 0.4);
  --el-button-text-color: var(--primary-color);
  --el-button-hover-bg-color: rgba(64, 158, 255, 0.3);
}

.test-result {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 600;
}

.test-result.success {
  background: rgba(46, 213, 115, 0.2);
  color: #2ed573;
  border: 1px solid rgba(46, 213, 115, 0.3);
}

.test-result.error {
  background: rgba(255, 71, 87, 0.2);
  color: #ff4757;
  border: 1px solid rgba(255, 71, 87, 0.3);
}

/* Dialog Footer */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* File Status Enhancement */
.file-status.connected {
  color: #2ed573;
  font-weight: 600;
}

.file-status.error {
  color: #ff4757;
  font-weight: 600;
}

.status-icon {
  margin-right: 4px;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-select) {
  width: 100%;
}

:deep(.el-select__wrapper) {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  transition: var(--transition-fast);
}

:deep(.el-select__wrapper:hover) {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}

:deep(.el-select__wrapper.is-focused) {
  border-color: var(--primary-color);
  box-shadow: 0 0 8px var(--shadow-primary);
}

:deep(.el-select__placeholder) {
  color: var(--text-muted);
}

:deep(.el-select__selected-item) {
  color: var(--text-primary);
}

:deep(.el-select__caret) {
  color: var(--text-secondary);
}

:deep(.el-button) {
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-color: var(--primary-color);
  color: var(--text-primary);
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
  border-color: var(--primary-light);
}

:deep(.el-button--success) {
  background: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%);
  border-color: var(--success-color);
  color: var(--text-primary);
}

:deep(.el-button--success:hover) {
  background: linear-gradient(135deg, var(--success-light) 0%, var(--success-color) 100%);
  border-color: var(--success-light);
}

:deep(.el-button--warning) {
  background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-dark) 100%);
  border-color: var(--warning-color);
  color: var(--text-primary);
}

:deep(.el-button--warning:hover) {
  background: linear-gradient(135deg, var(--warning-light) 0%, var(--warning-color) 100%);
  border-color: var(--warning-light);
}

:deep(.el-button--danger) {
  background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-dark) 100%);
  border-color: var(--danger-color);
  color: var(--text-primary);
}

:deep(.el-button--danger:hover) {
  background: linear-gradient(135deg, var(--danger-light) 0%, var(--danger-color) 100%);
  border-color: var(--danger-light);
}

:deep(.el-button.is-disabled) {
  background: var(--bg-tertiary) !important;
  border-color: var(--border-secondary) !important;
  color: var(--text-muted) !important;
  box-shadow: none !important;
  transform: none !important;
}

:deep(.el-button--small) {
  padding: 6px 12px;
  font-size: 12px;
  border-radius: var(--radius-sm);
}

/* 下拉菜单样式 */
:deep(.el-popper) {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
}

:deep(.el-select-dropdown__item) {
  color: var(--text-primary);
  background: transparent;
  transition: var(--transition-fast);
}

:deep(.el-select-dropdown__item:hover) {
  background: var(--bg-tertiary);
  color: var(--primary-color);
}

:deep(.el-select-dropdown__item.is-selected) {
  background: var(--primary-color);
  color: var(--text-primary);
  font-weight: 600;
}

/* Dialog样式 */
:deep(.el-dialog) {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--text-primary);
  padding: 16px 20px;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

:deep(.el-dialog__title) {
  color: var(--text-primary);
  font-weight: 600;
}

:deep(.el-dialog__headerbtn) {
  color: var(--text-primary);
}

:deep(.el-dialog__body) {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    gap: 16px;
    padding: 16px;
  }

  .main-content.realtime-layout {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
  }

  .section-content {
    padding: 16px;
  }

  .header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .input-type-selector {
    justify-content: center;
  }

  .upload-controls {
    justify-content: center;
  }

  .operation-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .operation-group {
    flex-direction: column;
    gap: 6px;
  }

  .header-operation-btn {
    width: 100%;
    justify-content: center;
  }

  .header-method-select {
    width: 100%;
    min-width: auto;
  }

  .preview-results-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .result-controls {
    flex-direction: column;
    gap: 6px;
  }

  .nav-controls-small {
    order: -1;
  }

  .charts-container {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 12px;
    height: auto;
    min-height: calc(100vh - 180px);
  }

  /* 非实时模式：垂直布局 */
  .main-content {
    grid-template-rows: auto auto 1fr;
    grid-template-areas:
      "data-input"
      "operations"
      "detection-results";
  }

  /* 实时模式：四个区域垂直布局 */
  .main-content.realtime-layout {
    grid-template-rows: auto auto auto auto;
    grid-template-areas:
      "data-input"
      "detection-results"
      "operations"
      "statistics-map";
  }

  .section {
    min-height: 300px;
  }

  .section-header {
    padding: 8px 16px;
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .section-title {
    font-size: 14px;
    text-align: center;
  }

  .header-controls {
    flex-direction: column;
    gap: 8px;
  }

  .action-btn {
    padding: 4px 8px;
    font-size: 11px;
  }

  .results-table {
    font-size: 12px;
  }

  .results-table th,
  .results-table td {
    padding: 8px;
  }

  .result-img {
    width: 100px;
    height: 67px;
  }

  .annotation-canvas {
    width: 100px;
    height: 67px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 8px;
    gap: 8px;
  }

  .section-content {
    padding: 12px;
  }

  .section-header {
    padding: 8px 16px;
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .section-title {
    font-size: 14px;
    text-align: center;
  }

  .section-title .icon {
    font-size: 16px;
  }

  .header-controls {
    flex-direction: column;
    gap: 6px;
  }

  .operation-controls {
    flex-direction: column;
    gap: 6px;
  }

  .operation-group {
    width: 100%;
  }

  .header-operation-btn {
    font-size: 11px;
    padding: 4px 8px;
  }

  .header-method-select {
    font-size: 11px;
  }

  .upload-btn {
    font-size: 12px;
    padding: 5px 10px;
  }

  .type-select {
    min-width: 100px;
  }

  .control-label {
    font-size: 12px;
  }

  .canvas-wrapper {
    padding: 8px;
  }

  .result-canvas {
    width: 200px;
    height: 150px;
  }

  .realtime-stats {
    flex-direction: column;
    gap: 8px;
  }

  /* Processing Status Overlay Responsive */
  .processing-status-overlay {
    padding: 8px;
  }

  .processing-status-card {
    padding: 12px 16px;
    min-width: 250px;
  }

  .status-title {
    font-size: 13px;
  }

  .status-percentage {
    font-size: 14px;
  }

  .status-progress-bar {
    height: 6px;
  }

  /* Details Dialog Responsive */
  .comparison-view,
  .video-comparison-view,
  .detection-comparison-view {
    flex-direction: column;
  }

  .overlapped-canvas-container {
    max-width: 100%;
  }

  .details-controls,
  .video-details-controls,
  .detection-details-controls {
    flex-direction: column;
    gap: 8px;
  }

  .zoom-controls,
  .detection-options {
    justify-content: center;
  }

  /* Detection Results Responsive */
  .results-table-container {
    padding: 8px 0;
  }

  .results-table .el-table__body td {
    padding: 8px 4px;
  }

  .table-actions {
    flex-direction: column;
    gap: 4px;
  }

  .table-actions .el-button {
    width: 100%;
  }

  .info-categories {
    margin-top: 2px;
  }

  .result-details-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .results-bottom-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .batch-operations {
    justify-content: center;
    flex-wrap: wrap;
  }

  .detection-summary {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
  }

  .modal-body {
    padding: 16px;
  }

  .modal-footer {
    padding: 12px 16px;
  }
}

/* 滚动条样式 */
.section-content::-webkit-scrollbar,
.results-table-container::-webkit-scrollbar {
  width: 6px;
}

.section-content::-webkit-scrollbar-track,
.results-table-container::-webkit-scrollbar-track {
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
}

.section-content::-webkit-scrollbar-thumb,
.results-table-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: var(--radius-sm);
}

.section-content::-webkit-scrollbar-thumb:hover,
.results-table-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
}

/* 图像类型显示样式 */
.image-type {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  margin-left: 8px;
  border: 1px solid;
}

.image-type.visible {
  background-color: #e8f5e8;
  color: #2e7d32;
  border-color: #4caf50;
}

.image-type.infrared {
  background-color: #fff3e0;
  color: #e65100;
  border-color: #ff9800;
}

/* Modify Dialog Styles */
.modify-dialog {
  .el-dialog__body {
    padding: 10px 20px;
  }
}

.modify-content {
  display: flex;
  gap: 20px;
  height: 70vh;
}

.modify-left {
  flex: 2;
  display: flex;
  flex-direction: column;
}

.modify-right {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.modify-image-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.modify-image-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.modify-section-title {
  margin: 0;
  font-size: 16px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modify-tools {
  display: flex;
  gap: 10px;
}

.tool-btn {
  display: flex;
  align-items: center;
  gap: 5px;
}

.modify-canvas-container {
  flex: 1;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  background: #f5f7fa;
}

.modify-canvas {
  width: 100%;
  height: 100%;
  cursor: crosshair;
}

.modify-canvas:hover {
  cursor: crosshair;
}

.modify-instructions {
  margin-top: 10px;
  padding: 10px;
  background: #f0f9ff;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.modify-instructions p {
  margin: 0 0 8px 0;
  font-weight: bold;
  color: #409eff;
}

.modify-instructions ul {
  margin: 0;
  padding-left: 20px;
}

.modify-instructions li {
  margin-bottom: 4px;
  color: #666;
}

.modify-detections-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.modify-detections-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.detection-count {
  font-size: 12px;
  color: #909399;
}

.modify-detections-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.detection-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.detection-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.detection-item.selected {
  border-color: #409eff;
  background: #e6f7ff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

.detection-info {
  margin-bottom: 8px;
}

.detection-category {
  margin-bottom: 8px;
}

.category-select {
  width: 100%;
}

.detection-confidence {
  display: flex;
  align-items: center;
  gap: 8px;
}

.confidence-label {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.confidence-input {
  flex: 1;
}

.confidence-unit {
  font-size: 12px;
  color: #666;
}

.detection-actions {
  display: flex;
  justify-content: flex-end;
}

.add-category-section {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
  background: #fafafa;
}

.add-category-section h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #333;
}

.add-category-form {
  display: flex;
  gap: 8px;
}

.category-input {
  flex: 1;
}

.modify-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Fix for dialog auto-resize issue */
.details-dialog .el-dialog {
  resize: none !important;
  min-width: 90% !important;
  max-width: 95% !important;
  min-height: 80vh !important;
  max-height: 90vh !important;
}

.details-dialog .el-dialog__body {
  height: 70vh !important;
  overflow: hidden !important;
}

.details-content {
  height: 100% !important;
  overflow: hidden !important;
}

.detection-details-container {
  height: 100% !important;
  overflow: hidden !important;
}
</style>
