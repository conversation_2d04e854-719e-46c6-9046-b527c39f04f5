# 样本标注更新API文档

## 概述

本文档描述了新增的样本标注更新API接口，用于支持前端修正界面的标注数据持久化功能。

## API接口详情

### 更新样本标注

**接口路径:** `POST /api/exp/updateArmExpAnnotation`

**功能描述:** 更新指定样本的标注内容，将修正后的YOLO格式标注数据保存到对应的标注文件中。

#### 请求参数

**Content-Type:** `application/json`

**请求体结构:**
```json
{
  "id": 1,
  "annotation_content": "0 0.5 0.5 0.2 0.3\n1 0.3 0.7 0.15 0.25"
}
```

**参数说明:**
- `id` (int, 必需): 样本ID
- `annotation_content` (string, 必需): YOLO格式的标注内容，多行用`\n`分隔

#### YOLO格式说明

每行格式: `class_id center_x center_y width height`
- `class_id`: 类别ID (整数)
- `center_x`: 边界框中心点X坐标 (相对坐标，0-1之间)
- `center_y`: 边界框中心点Y坐标 (相对坐标，0-1之间)  
- `width`: 边界框宽度 (相对坐标，0-1之间)
- `height`: 边界框高度 (相对坐标，0-1之间)

#### 响应格式

**成功响应 (200):**
```json
{
  "status": "success",
  "message": "标注更新成功",
  "data": {
    "sample_id": 1,
    "annotation_path": "/path/to/annotation.txt",
    "content_length": 45
  }
}
```

**错误响应:**

1. **样本不存在 (404):**
```json
{
  "detail": "样本不存在"
}
```

2. **样本无关联文件 (400):**
```json
{
  "detail": "样本没有关联文件"
}
```

3. **文件写入失败 (500):**
```json
{
  "detail": "写入标注文件失败: [具体错误信息]"
}
```

## 业务流程

1. **前端修正界面操作:**
   - 用户在修正界面中编辑检测框
   - 添加、删除、移动检测框
   - 修改类别和置信度

2. **保存操作:**
   - 点击"保存修改"按钮
   - 前端将检测结果转换为YOLO格式
   - 调用`updateArmExpAnnotation`API

3. **后端处理:**
   - 验证样本是否存在
   - 获取标注文件路径
   - 创建必要的目录结构
   - 写入新的标注内容
   - 更新样本时间戳

4. **响应处理:**
   - 成功：更新前端显示，显示成功消息
   - 失败：显示错误消息，保持当前状态

## 文件系统结构

标注文件与图片文件的对应关系:
```
workspace/Samples/category_id/
├── image1.jpg          # 图片文件
├── image1.txt          # 对应的标注文件
├── image2.png
├── image2.txt
└── ...
```

## 错误处理

API实现了完善的错误处理机制:

1. **输入验证:** 检查必需参数是否存在
2. **样本验证:** 确认样本在数据库中存在
3. **文件路径验证:** 确保能够确定标注文件路径
4. **目录创建:** 自动创建必要的目录结构
5. **文件写入保护:** 使用异常处理确保文件操作安全
6. **数据库更新:** 可选的时间戳更新，失败不影响主要功能

## 安全考虑

1. **路径安全:** 使用`get_annotation_file_path`函数确保文件路径安全
2. **输入验证:** 验证样本ID的有效性
3. **权限控制:** 确保只能修改有权限的样本
4. **文件编码:** 使用UTF-8编码确保中文支持

## 测试

使用提供的`test_annotation_api.py`脚本进行API测试:

```bash
python test_annotation_api.py
```

测试脚本会验证:
- 样本是否存在
- API接口是否正常响应
- 标注更新是否成功

## 前端集成

前端已完成集成，相关文件:
- `src/api/api.js`: 添加了`APIUpdateSampleAnnotation`函数
- `src/views/sample.vue`: 修改了`saveModifiedResult`方法

## 部署注意事项

1. 确保后端服务器有写入标注文件的权限
2. 确保`workspace/Samples`目录结构存在
3. 确保数据库连接正常
4. 确保`utils.tools`中的辅助函数可用
