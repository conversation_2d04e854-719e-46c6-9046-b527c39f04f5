// 引入路由
import { createRouter, createWebHashHistory } from "vue-router";

import LoginView from "@/views/login.vue";
import UserView from "@/views/user.vue";
import ManageView from "@/views/manager.vue";
import SampleView from "@/views/sample.vue";
import DetectedView from "@/views/detected.vue";

const router = createRouter({
  history:createWebHashHistory(),
  routes:[
    {
      path:'/',
      component:LoginView
    },
    {
      path:'/user',
      component:UserView
    },
    {
      path:'/manager',
      component:ManageView
    },
    {
      path:'/sample',
      component:SampleView
    },
    {
      path:'/detected',
      component:DetectedView
    },
    
  ]
})
// 导出 router
export default router;