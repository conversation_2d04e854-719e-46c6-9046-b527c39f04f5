/**
 * 大量文件上传功能测试
 * 用于验证10000+文件上传的功能和性能
 */

// 模拟文件创建函数
function createMockFile(name, size = 1024, type = 'image/jpeg') {
  const file = new File(['mock content'], name, { type });
  Object.defineProperty(file, 'size', { value: size });
  return file;
}

// 创建大量测试文件
function createTestFiles(count) {
  const files = [];
  for (let i = 0; i < count; i++) {
    const fileName = `test_image_${i.toString().padStart(5, '0')}.jpg`;
    files.push(createMockFile(fileName, 1024 * (10 + Math.random() * 90))); // 10-100KB
  }
  return files;
}

// 测试用例
const testCases = [
  {
    name: '小批量文件上传测试',
    fileCount: 50,
    expectedBatches: 1,
    description: '测试小于100个文件的直接上传'
  },
  {
    name: '中等批量文件上传测试',
    fileCount: 500,
    expectedBatches: 10,
    description: '测试500个文件的分批上传'
  },
  {
    name: '大批量文件上传测试',
    fileCount: 2000,
    expectedBatches: 40,
    description: '测试2000个文件的分批上传'
  },
  {
    name: '超大批量文件上传测试',
    fileCount: 10000,
    expectedBatches: 200,
    description: '测试10000个文件的分批上传'
  }
];

// 模拟Vue组件实例
class MockSampleComponent {
  constructor() {
    this.uploadProgress = {
      show: false,
      current: 0,
      total: 0,
      currentBatch: 0,
      totalBatches: 0,
      percentage: 0,
      status: 'ready',
      message: '',
      cancelled: false,
      results: {
        totalFiles: 0,
        successCount: 0,
        failedCount: 0,
        imageCount: 0,
        annotationCount: 0,
        failedBatches: []
      }
    };
    this.currentCategory = { id: 'test_category' };
  }

  // 模拟上传样本方法
  async uploadSamples(files) {
    console.log(`开始上传 ${files.length} 个文件`);
    
    // 初始化上传状态
    this.uploadProgress = {
      show: true,
      current: 0,
      total: files.length,
      currentBatch: 0,
      totalBatches: 0,
      percentage: 0,
      status: 'uploading',
      message: '准备上传...',
      cancelled: false,
      results: {
        totalFiles: files.length,
        successCount: 0,
        failedCount: 0,
        imageCount: 0,
        annotationCount: 0,
        failedBatches: []
      }
    };

    try {
      if (files.length <= 100) {
        await this.uploadBatch(files, 0, 1);
      } else {
        await this.uploadLargeFileSet(files);
      }
      await this.handleUploadComplete();
    } catch (error) {
      console.error('上传失败:', error);
      this.uploadProgress.status = 'error';
    }
  }

  // 模拟大文件集分批上传
  async uploadLargeFileSet(files) {
    const BATCH_SIZE = 50;
    const batches = [];
    
    for (let i = 0; i < files.length; i += BATCH_SIZE) {
      batches.push(files.slice(i, i + BATCH_SIZE));
    }

    this.uploadProgress.totalBatches = batches.length;
    console.log(`分批上传，共${batches.length}批`);

    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      if (this.uploadProgress.cancelled) {
        console.log('上传已取消');
        return;
      }

      this.uploadProgress.currentBatch = batchIndex + 1;
      console.log(`上传第${batchIndex + 1}/${batches.length}批`);
      
      try {
        await this.uploadBatch(batches[batchIndex], batchIndex, batches.length);
        
        this.uploadProgress.current += batches[batchIndex].length;
        this.uploadProgress.percentage = Math.round(
          (this.uploadProgress.current / this.uploadProgress.total) * 100
        );
        
        // 模拟批次间延迟
        if (batchIndex < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100)); // 减少测试时间
        }
      } catch (error) {
        console.error(`第${batchIndex + 1}批上传失败:`, error);
        this.uploadProgress.results.failedCount += batches[batchIndex].length;
      }
    }
  }

  // 模拟上传单个批次
  async uploadBatch(files, batchIndex, totalBatches) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 50));
    
    // 模拟成功上传
    const imageCount = files.filter(f => f.type.startsWith('image/')).length;
    const annotationCount = files.filter(f => f.name.endsWith('.txt')).length;
    
    this.uploadProgress.results.successCount += files.length;
    this.uploadProgress.results.imageCount += imageCount;
    this.uploadProgress.results.annotationCount += annotationCount;
    
    console.log(`批次${batchIndex + 1}上传成功: ${imageCount}图片, ${annotationCount}标注`);
  }

  // 模拟处理上传完成
  async handleUploadComplete() {
    if (this.uploadProgress.cancelled) {
      this.uploadProgress.status = 'cancelled';
      console.log('上传已取消');
      return;
    }
    
    this.uploadProgress.status = 'completed';
    this.uploadProgress.percentage = 100;
    console.log('上传完成!', this.uploadProgress.results);
  }

  // 模拟取消上传
  cancelUpload() {
    this.uploadProgress.cancelled = true;
    console.log('取消上传请求');
  }
}

// 运行测试
async function runTests() {
  console.log('开始大量文件上传功能测试...\n');
  
  for (const testCase of testCases) {
    console.log(`\n=== ${testCase.name} ===`);
    console.log(`描述: ${testCase.description}`);
    console.log(`文件数量: ${testCase.fileCount}`);
    console.log(`预期批次: ${testCase.expectedBatches}`);
    
    const startTime = Date.now();
    const files = createTestFiles(testCase.fileCount);
    const component = new MockSampleComponent();
    
    await component.uploadSamples(files);
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`实际批次: ${component.uploadProgress.totalBatches || 1}`);
    console.log(`上传状态: ${component.uploadProgress.status}`);
    console.log(`成功文件: ${component.uploadProgress.results.successCount}`);
    console.log(`耗时: ${duration}ms`);
    console.log(`平均速度: ${Math.round(testCase.fileCount / (duration / 1000))} 文件/秒`);
    
    // 验证结果
    const success = component.uploadProgress.results.successCount === testCase.fileCount &&
                   (component.uploadProgress.totalBatches || 1) === testCase.expectedBatches;
    
    console.log(`测试结果: ${success ? '✅ 通过' : '❌ 失败'}`);
  }
  
  console.log('\n=== 取消上传测试 ===');
  const cancelTest = new MockSampleComponent();
  const cancelFiles = createTestFiles(1000);
  
  // 开始上传
  const uploadPromise = cancelTest.uploadSamples(cancelFiles);
  
  // 500ms后取消
  setTimeout(() => {
    cancelTest.cancelUpload();
  }, 500);
  
  await uploadPromise;
  
  console.log(`取消测试结果: ${cancelTest.uploadProgress.status === 'cancelled' ? '✅ 通过' : '❌ 失败'}`);
  console.log(`已上传文件: ${cancelTest.uploadProgress.results.successCount}`);
  
  console.log('\n测试完成!');
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.runLargeUploadTests = runTests;
  console.log('测试函数已加载，请在控制台运行: runLargeUploadTests()');
} else {
  // Node.js环境
  runTests().catch(console.error);
}

export { runTests, createTestFiles, MockSampleComponent };
