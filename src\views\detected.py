from __future__ import annotations

import pathlib, sys

from PIL import Image

ROOT = pathlib.Path(__file__).resolve().parent
sys.path.insert(0, str(ROOT))

import cv2
from .Dehaze.Dehaze import Dehaze
from .LowlightEnhance.LowlightEnhancer import LowlightEnhancer
from .MotionDeblur.MotionDeblurrer import MotionDeblurrer
from .ToolBox import ToolBox
from .toChinese import Tool
from detector.Detector_124 import Detector_YOLO, Detector_Small
from detector.RTDETR.Detector_RTDETR import Detector_RTDETR
from detector.Detector_YOLOE import run_this

dehaze = Dehaze()
lowlight_enhance = LowlightEnhancer()
motion_deblur = MotionDeblurrer()
# 获取当前目录和权重目录
current_dir = pathlib.Path(__file__).parent.resolve()
weights_dir = current_dir / 'detector' / 'weights'

# 定义检测器初始化映射
detectors = {
    'method_1': {
        'vl': (Detector_YOLO, 'v8_vl_new.pt'),
        'ir': (Detector_YOLO, 'v8_ir.pt')
    },
    'method_2': {
        'vl': (Detector_YOLO, 'v12_vl.pt'),
        'ir': (Detector_YOLO, 'v12_ir.pt')
    },
    'method_3': {
        'vl': (Detector_RTDETR, 'rt_detr_vl.pth'),
        'ir': (Detector_RTDETR, 'rt_detr_ir.pth')
    },
    'method_4': {
        'vl': (Detector_Small, 'v8_vl.pt'),
        'ir': (Detector_Small, 'v8_ir.pt')
    }
}

# 初始化所有检测器
detector_instances = {}
for method, types in detectors.items():
    for sensor, (cls, weight_file) in types.items():
        key = f"{method}_{sensor}"
        detector_instances[key] = cls(weights_dir / weight_file)

# 添加 method_5
detector_instances['method_5'] = run_this()

# 解包到原始变量名
method_1_vl = detector_instances['method_1_vl']
method_1_ir = detector_instances['method_1_ir']
method_2_vl = detector_instances['method_2_vl']
method_2_ir = detector_instances['method_2_ir']
method_3_vl = detector_instances['method_3_vl']
method_3_ir = detector_instances['method_3_ir']
method_4_vl = detector_instances['method_4_vl']
method_4_ir = detector_instances['method_4_ir']
method_5 = detector_instances['method_5']

from fastapi import APIRouter, File, Form, HTTPException, UploadFile, status
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel, Field
import os
import uuid
import numpy as np
import time
import shutil
import json
from typing import List, Dict, Any, Optional

WORK = ROOT.parent

router = APIRouter(prefix="/api/detected", tags=["detected"])

# 创建工作区目录结构
WORKSPACE_DIR = WORK / "workspace"
WORKSPACE_DIR.mkdir(parents=True, exist_ok=True)

# 确保上传目录存在
UPLOAD_DIR = WORKSPACE_DIR / "uploads"
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

# 创建预处理和检测目录
PREPROCESSED_DIR = WORKSPACE_DIR / "preprocessed"
PREPROCESSED_DIR.mkdir(exist_ok=True)

DETECTED_DIR = WORKSPACE_DIR / "detected"
DETECTED_DIR.mkdir(exist_ok=True)

# 创建预处理子目录
PREPROCESS_SUBDIRS = {
    "dehaze": "dehaze",
    "light": "light",
    "motion": "motion",
    "denoise": "denoise",
    "superres": "superres"
}

for subdir in PREPROCESS_SUBDIRS.values():
    (PREPROCESSED_DIR / subdir).mkdir(exist_ok=True)

# 创建检测子目录
DETECTION_SUBDIRS = {
    "detection": "detection",
    "fewshot": "fewshot"
}

for subdir in DETECTION_SUBDIRS.values():
    (DETECTED_DIR / subdir).mkdir(exist_ok=True)

# 文件管理数据库（简单的内存存储，生产环境应使用真实数据库）
FILE_DATABASE = {}

# 文件信息模型
class FileInfo(BaseModel):
    uuid: str
    original_name: str
    file_path: str
    file_type: str
    upload_time: float
    file_size: int
    processed_files: Dict[str, str] = {}  # 预处理方法 -> 处理后文件路径
    image_type: str = "unknown"  # 图像类型: "visible" (可见光) 或 "infrared" (红外)

# 预处理方法映射
PREPROCESS_METHODS = {
    "dehaze": "dehaze",
    "light": "light",
    "motion": "motion",
    "denoise": "denoise",
    "superres": "superres"
}

# 检测方法映射
DETECTION_METHODS = {
    "detection": "detection",
    "fewshot": "fewshot"
}

# 图像类型判别算法
# def classify_image_type(image):
#     """
#     判别图像是可见光图像还是红外图像
#
#     Args:
#         image: OpenCV格式的图像 (BGR)
#
#     Returns:
#         str: "visible" (可见光) 或 "infrared" (红外)
#     """
#     try:
#         # 转换为灰度图像
#         gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
#
#         # 计算图像统计特征
#         mean_intensity = np.mean(gray)
#         std_intensity = np.std(gray)
#
#         # 计算直方图
#         hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
#         hist_normalized = hist.flatten() / hist.sum()
#
#         # 特征1: 亮度分布特征
#         # 红外图像通常具有较低的平均亮度和较大的标准差
#         brightness_score = mean_intensity / 255.0
#         contrast_score = std_intensity / 128.0
#
#         # 特征2: 直方图分布特征
#         # 计算直方图的峰值分布
#         hist_peaks = []
#         for i in range(1, 255):
#             if hist_normalized[i] > hist_normalized[i-1] and hist_normalized[i] > hist_normalized[i+1]:
#                 if hist_normalized[i] > 0.01:  # 只考虑显著的峰值
#                     hist_peaks.append(i)
#
#         # 特征3: 边缘密度特征
#         # 红外图像通常边缘较少且模糊
#         edges = cv2.Canny(gray, 50, 150)
#         edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
#
#         # 特征4: 颜色信息特征
#         # 分析BGR通道的差异
#         b_channel = image[:, :, 0].astype(np.float32)
#         g_channel = image[:, :, 1].astype(np.float32)
#         r_channel = image[:, :, 2].astype(np.float32)
#
#         # 计算通道间的相关性
#         bg_corr = np.corrcoef(b_channel.flatten(), g_channel.flatten())[0, 1]
#         gr_corr = np.corrcoef(g_channel.flatten(), r_channel.flatten())[0, 1]
#         br_corr = np.corrcoef(b_channel.flatten(), r_channel.flatten())[0, 1]
#
#         # 红外图像的RGB通道通常高度相关（接近灰度）
#         color_correlation = (bg_corr + gr_corr + br_corr) / 3.0
#
#         # 特征5: 温度映射特征（伪彩色检测）
#         # 检测是否存在典型的红外伪彩色映射
#         hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
#         h_channel = hsv[:, :, 0]
#
#         # 统计色调分布
#         hue_hist = cv2.calcHist([h_channel], [0], None, [180], [0, 180])
#         hue_hist_normalized = hue_hist.flatten() / hue_hist.sum()
#
#         # 红外伪彩色图像通常在特定色调范围内有集中分布
#         red_hue_ratio = np.sum(hue_hist_normalized[0:10]) + np.sum(hue_hist_normalized[170:180])  # 红色色调
#         blue_hue_ratio = np.sum(hue_hist_normalized[100:140])  # 蓝色色调
#
#         # 综合判别逻辑
#         infrared_score = 0
#
#         # 规则1: 高通道相关性 (红外图像RGB通道相似)
#         if color_correlation > 0.95:
#             infrared_score += 2
#         elif color_correlation > 0.9:
#             infrared_score += 1
#
#         # 规则2: 低边缘密度 (红外图像边缘较少)
#         if edge_density < 0.05:
#             infrared_score += 2
#         elif edge_density < 0.1:
#             infrared_score += 1
#
#         # 规则3: 特殊亮度分布 (红外图像通常较暗或有特殊分布)
#         if brightness_score < 0.3 or brightness_score > 0.8:
#             infrared_score += 1
#
#         # 规则4: 伪彩色特征 (红外图像可能有红蓝伪彩色)
#         if red_hue_ratio > 0.3 or blue_hue_ratio > 0.3:
#             infrared_score += 1
#
#         # 规则5: 直方图峰值特征 (红外图像通常峰值较少)
#         if len(hist_peaks) <= 2:
#             infrared_score += 1
#
#         # 最终判别
#         if infrared_score >= 3:
#             image_type = "infrared"
#         else:
#             image_type = "visible"
#
#         print(f"图像类型判别结果: {image_type}")
#         print(f"  亮度分数: {brightness_score:.3f}")
#         print(f"  对比度分数: {contrast_score:.3f}")
#         print(f"  边缘密度: {edge_density:.3f}")
#         print(f"  颜色相关性: {color_correlation:.3f}")
#         print(f"  红外分数: {infrared_score}/6")
#
#         return image_type
#
#     except Exception as e:
#         print(f"图像类型判别失败: {e}")
#         return "visible"  # 默认返回可见光


def classify_image_type(image, threshold=1000):
    """
    通过图像尺寸判断图像是可见光还是红外（针对大疆无人机拍摄的图像）

    参数:
    image: 可以是文件路径、numpy数组或PIL Image对象
    threshold: 分辨率阈值，低于此值判断为红外图像（默认1000像素）

    返回:
    str: "可见光" 或 "红外" 或 "未知"
    """
    # 处理不同类型的输入
    if isinstance(image, str):
        # 如果是文件路径，读取图像
        if not os.path.exists(image):
            raise ValueError("图像文件不存在")

        # 使用PIL获取图像尺寸而不完全加载图像
        try:
            with Image.open(image) as img:
                width, height = img.size
        except Exception as e:
            # 如果PIL无法读取，尝试使用OpenCV
            img = cv2.imread(image)
            if img is None:
                raise ValueError("无法读取图像文件")
            height, width = img.shape[:2]
    elif isinstance(image, np.ndarray):
        # 如果是numpy数组
        height, width = image.shape[:2]
    elif hasattr(image, 'size'):
        # 如果是PIL Image对象
        width, height = image.size
    else:
        raise TypeError("不支持的图像格式")

    # 打印图像尺寸信息（用于调试）
    print(f"图像尺寸: {width} x {height} 像素")

    # 根据尺寸判断图像类型
    # 大疆无人机的红外图像通常分辨率较低（如640×512）
    # 可见光图像通常分辨率较高（如4000×3000）
    if width <= threshold or height <= threshold:
        return "infrared"
    else:
        return "visible"

# 文件上传端点
@router.post("/upload")
async def upload_files(files: List[UploadFile] = File(...)):
    """
    上传单个或多个文件
    返回格式匹配前端期望的结构
    """
    try:
        uploaded_files = []

        for file in files:
            # 生成唯一文件名防止冲突
            file_ext = os.path.splitext(file.filename)[1].lower()
            file_uuid = uuid.uuid4().hex
            unique_filename = f"{file_uuid}{file_ext}"
            file_path = UPLOAD_DIR / unique_filename

            # 保存文件
            content = await file.read()
            with open(file_path, "wb") as f:
                f.write(content)

            # 确定文件类型
            if file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                file_type = 'image'
            elif file_ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv']:
                file_type = 'video'
            else:
                file_type = 'unknown'

            # 判别图像类型（可见光/红外）
            image_type = "unknown"
            if file_type == 'image':
                try:
                    # 读取图像进行类型判别
                    img = cv2.imread(str(file_path))
                    if img is not None:
                        image_type = classify_image_type(img)
                        print(f"图像 {file.filename} 判别为: {image_type}")
                    else:
                        print(f"无法读取图像文件 {file.filename}，默认为可见光")
                        image_type = "visible"
                except Exception as e:
                    print(f"图像类型判别失败 {file.filename}: {e}，默认为可见光")
                    image_type = "visible"
            elif file_type == 'video':
                try:
                    # 读取视频第一帧进行类型判别
                    cap = cv2.VideoCapture(str(file_path))
                    ret, frame = cap.read()
                    cap.release()

                    if ret and frame is not None:
                        image_type = classify_image_type(frame)
                        print(f"视频 {file.filename} 通过第一帧判别为: {image_type}")
                    else:
                        print(f"无法读取视频文件 {file.filename}，默认为可见光")
                        image_type = "visible"
                except Exception as e:
                    print(f"视频类型判别失败 {file.filename}: {e}，默认为可见光")
                    image_type = "visible"

            # 创建文件信息
            file_info = FileInfo(
                uuid=file_uuid,
                original_name=file.filename,
                file_path=f"workspace/uploads/{unique_filename}",
                file_type=file_type,
                upload_time=time.time(),
                file_size=len(content),
                image_type=image_type
            )

            # 存储到文件数据库
            FILE_DATABASE[file_uuid] = file_info

            uploaded_files.append({
                "uuid": file_uuid,
                "original_name": file.filename,
                "file_path": f"workspace/uploads/{unique_filename}",
                "file_type": file_type,
                "file_size": len(content),
                "image_type": image_type
            })

        # 根据文件数量返回不同格式
        if len(uploaded_files) == 1:
            return {
                "status": "success",
                "message": "文件上传成功",
                "data": {
                    "file_info": uploaded_files[0]
                }
            }
        else:
            return {
                "status": "success",
                "message": f"文件上传成功，共{len(uploaded_files)}个文件",
                "data": {
                    "files": uploaded_files
                }
            }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"文件上传失败: {str(e)}"
        )


# 处理请求模型
class ProcessRequest(BaseModel):
    file_url: str = Field(None, description="单文件URL")
    file_urls: List[str] = Field(None, description="多文件URL列表")
    current_index: int = Field(0, description="当前文件索引")
    rtsp_url: str = Field(None, description="RTSP流地址")
    preprocess: str = Field("", description="预处理方法")
    detection: str = Field("", description="检测方法")

# 新的预处理请求模型
class PreprocessRequest(BaseModel):
    file_type: str = Field(..., description="文件类型: image, video, folder")
    file_uuid: str = Field(..., description="文件UUID")
    method: str = Field("defog", description="预处理方法")

# 新的检测请求模型
class DetectRequest(BaseModel):
    file_type: str = Field(..., description="文件类型: image, video, folder")
    file_uuid: str = Field(..., description="文件UUID")
    input_source: str = Field("original", description="输入源: original 或 preprocessed")
    model: str = Field("yolov8", description="检测模型")


# 在文件顶部添加预处理函数映射
PREPROCESS_FUNCTIONS_IMAGE = {
    "dehaze": "dehaze_image",
    "light": "adjust_lighting",
    "motion": "reduce_motion_blur",
    "denoise": "denoise_image",
    "superres": "super_resolution"
}
PREPROCESS_FUNCTIONS_VIDEO = {
    "dehaze": "dehaze_video",
    "light": "adjust_lighting_video",
    "motion": "reduce_motion_blur_video",
    "denoise": "denoise_image_video",
    "superres": "super_resolution_video"
}

# 添加实际的预处理函数
def dehaze_image(image):
    return dehaze.img_dehaze(img_input=image)
def dehaze_image_video(image):
    return dehaze.img_dehaze(img_input=image)
def adjust_lighting(image):
    return lowlight_enhance.enhance(img_input=image)
def adjust_lighting_video(path):
    return lowlight_enhance.enhance_video(video_path=path)

def reduce_motion_blur(image):
    return motion_deblur.deblur(img_input=image)

def reduce_motion_blur_video(path):
    return motion_deblur.deblur_video(video_path=path)

def denoise_image(image):
    return ToolBox.denoise(image)
def denoise_image_video(path):
    return ToolBox.process_video(path,ToolBox.denoise)

def super_resolution(image):
    return ToolBox.upsize(image)
def super_resolution_video(path):
    return ToolBox.process_video(path,ToolBox.upsize)

# 处理文件夹中所有文件的预处理
def handle_all_files_preprocess(request: ProcessRequest):
    """处理文件夹中的所有图片文件"""
    processed_files = []
    failed_files = []

    for i, file_url in enumerate(request.file_urls):
        try:
            file_path = WORK / file_url.lstrip("/")

            if not file_path.exists():
                failed_files.append({"index": i, "url": file_url, "error": "文件不存在"})
                continue

            # 只处理图片文件
            if file_path.suffix.lower() not in [".jpg", ".jpeg", ".png"]:
                failed_files.append({"index": i, "url": file_url, "error": "不支持的文件类型"})
                continue

            # 创建单文件处理请求
            single_request = ProcessRequest(
                file_url=file_url,
                preprocess=request.preprocess,
                detection=""  # 只进行预处理
            )

            # 处理单个文件
            result = handle_image(file_path, single_request)

            if result.get("success"):
                processed_files.append({
                    "index": i,
                    "original_url": file_url,
                    "processed_url": result["result_url"],
                    "process_steps": result.get("process_steps", [])
                })
            else:
                failed_files.append({"index": i, "url": file_url, "error": "处理失败"})

        except Exception as e:
            failed_files.append({"index": i, "url": file_url, "error": str(e)})

    return {
        "success": True,
        "processed_files": processed_files,
        "failed_files": failed_files,
        "total_files": len(request.file_urls),
        "processed_count": len(processed_files),
        "failed_count": len(failed_files),
        "current_index": request.current_index,
        "result_url": processed_files[request.current_index]["processed_url"] if processed_files and request.current_index < len(processed_files) else None
    }


# --- 检测辅助函数 ---
def _run_image_detection(image, image_type="visible", model="yolov8"):
    """运行图像检测并返回YOLO格式结果"""
    start_t = time.time()
    try:
        # 根据图像类型选择检测模型
        if image_type == "infrared":
            print(f"使用红外检测模型{model}")
            if model == "yolov8":
                bboxes = method_1_ir.predict(image)
            elif model == "yolov12":
                bboxes = method_2_ir.predict(image)
            elif model == "detr":
                bboxes = method_3_ir.predict(image)
            elif model == "samll":
                bboxes = method_4_ir.predict(image)
            elif model == "sample":
                bboxes = method_5._2_predict(image)
        else:
            print(f"使用可见光检测模型{model}")
            if model == "yolov8":
                bboxes = method_1_vl.predict(image)
            elif model == "yolov12":
                bboxes = method_2_vl.predict(image)
            elif model == "detr":
                bboxes = method_3_vl.predict(image)
            elif model == "samll":
                bboxes = method_4_vl.predict(image)
            elif model == "sample":
                bboxes = method_5._2_predict(image)
    except Exception as e:
        print(f"检测模型执行失败: {e}")
        bboxes = []
    elapsed = time.time() - start_t

    detections = []
    confidences = []
    class_ids = set()

    # 类别映射
    class_map = {
        0: '武装人员',
        1: '运输车',
        2: '突击车',
        3: '82速射迫击炮',
        4: '轮式突击炮',
        5: '防坦克三角锥',
        6: '坦克',
        7: '地堡',
        8: '步兵战车',
        9: '榴弹炮',
        10: '伪装网',
        11: '无人装备'
    }

    for b in bboxes:
        # 兼容 [cls, xc, yc, w, h, conf]
        cls, xc, yc, wb, hb = map(float, b[:5])
        conf = float(b[5]) if len(b) > 5 else 0.0

        # 转换为前端期望的YOLO格式
        detections.append({
            "category": class_map.get(int(cls), f"class_{int(cls)}"),
            "confidence": conf * 100,  # 转换为百分比
            "boundingBox": {
                "center_x": xc,
                "center_y": yc,
                "width": wb,
                "height": hb
            }
        })
        confidences.append(conf)
        class_ids.add(int(cls))

    stats = {
        "total_objects": len(detections),
        "unique_classes": len(class_ids),
        "processing_time": round(elapsed, 3),
        "avg_confidence": round(sum(confidences) / len(confidences), 4) if confidences else 0.0
    }
    return detections, stats, bboxes

async def detect_image_file(file_path: pathlib.Path, file_info: FileInfo, model="yolo8"):
    """检测图像文件"""
    # 读取图像
    img = cv2.imread(str(file_path))
    if img is None:
        raise HTTPException(status_code=400, detail="无法读取图像文件")

    # 获取图像类型
    image_type = file_info.image_type
    print(f"检测图像文件: {file_info.original_name}, 图像类型: {image_type}")

    # 执行检测
    detections, stats, bboxes = _run_image_detection(img, image_type, model)
    result_path = _save_detection_visual(img, bboxes, file_path.stem)

    relative_path = result_path.relative_to(WORK)
    origin_relative_path = file_path.relative_to(WORK)

    return {
        "image": str(relative_path).replace('\\', '/'),
        "origin_image": str(origin_relative_path).replace('\\', '/'),
        "detections": detections,
        "has_detections": len(detections) > 0,
        "timestamp": time.time(),
        "image_type": image_type
    }

def _save_detection_visual(image, bboxes, base_filename: str) -> pathlib.Path:
    # 叠加可视化并保存
    clsmap = {
        0: '武装人员',
        1: '运输车',
        2: '突击车',
        3: '82速射迫击炮',
        4: '轮式突击炮',
        5: '防坦克三角锥',
        6: '坦克',
        7: '地堡',
        8: '步兵战车',
        9: '榴弹炮',
        10: '伪装网',
        11: '无人装备'
    }
    vis_img = Tool.bboxes_list2img(bboxes, image,class_map=clsmap)
    save_dir = DETECTED_DIR / DETECTION_SUBDIRS.get("detection", "detection")
    save_dir.mkdir(parents=True, exist_ok=True)
    result_filename = f"{base_filename}_det_{int(time.time())}.jpg"
    result_path = save_dir / result_filename
    cv2.imwrite(str(result_path), vis_img)
    return result_path


# 修改handle_image函数，支持只进行预处理
def handle_image(file_path: pathlib.Path, request: ProcessRequest):
    # 读取原始图像
    original_image = cv2.imread(str(file_path))
    if original_image is None:
        raise HTTPException(status_code=400, detail="无法读取图像文件")

    # 处理后的图像
    processed_image = original_image.copy()
    process_steps = []

    # 如果有预处理
    if request.preprocess and request.preprocess in PREPROCESS_FUNCTIONS_IMAGE:
        # 获取预处理函数名并执行
        func_name = PREPROCESS_FUNCTIONS_IMAGE[request.preprocess]
        if func_name in globals():
            processed_image = globals()[func_name](processed_image)
            process_steps.append(PREPROCESS_METHODS.get(request.preprocess, "预处理"))

    # 确定保存目录
    save_dir = PREPROCESSED_DIR
    if request.preprocess in PREPROCESS_SUBDIRS:
        save_dir = save_dir / PREPROCESS_SUBDIRS[request.preprocess]

    # 确保目录存在
    save_dir.mkdir(parents=True, exist_ok=True)

    # 生成结果文件名
    result_filename = f"processed_{file_path.stem}_{int(time.time())}{file_path.suffix}"
    result_path = save_dir / result_filename

    # 保存处理后的图像
    cv2.imwrite(str(result_path), processed_image)

    # 返回结果URL
    relative_path = result_path.relative_to(WORK)
    result_url = f"{relative_path}"

    return {
        "success": True,
        "result_url": result_url,
        "process_steps": process_steps,
        "save_path": str(result_path)
    }

@router.post("/smallInit")
async def smallInit(request: DetectRequest):
    try:
        # 检查文件是否存在
        if request.file_uuid not in FILE_DATABASE:
            raise HTTPException(status_code=404, detail="文件不存在")

        file_info = FILE_DATABASE[request.file_uuid]
        # 使用原始文件
        file_path = WORK / file_info.file_path

        img = cv2.imread(str(file_path))
        method_5._1_refer_init(img, max_wh=[1280, 720])

        return {
            "code": 200,
            "message": "预训练完成",
            "data": {
                "results": []
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"检测失败: {str(e)}"
        )

# 新的检测端点
@router.post("/detect")
async def detect_data(request: DetectRequest):
    """
    检测文件中的目标
    接受file_type, file_uuid, input_source参数，返回YOLO格式的检测结果
    """
    try:
        # 检查文件是否存在
        if request.file_uuid not in FILE_DATABASE:
            raise HTTPException(status_code=404, detail="文件不存在")

        file_info = FILE_DATABASE[request.file_uuid]

        # 根据input_source选择文件路径
        if request.input_source == "preprocessed":
            # 使用预处理后的文件
            if not file_info.processed_files:
                raise HTTPException(status_code=400, detail="没有预处理文件可用")
            # 使用最新的预处理文件（或指定方法的文件）
            processed_path = list(file_info.processed_files.values())[-1]
            file_path = WORK / processed_path
        else:
            # 使用原始文件
            file_path = WORK / file_info.file_path

        if not file_path.exists():
            raise HTTPException(status_code=404, detail="文件不存在")

        # 根据文件类型执行检测
        if file_info.file_type == 'image':
            result = await detect_image_file(file_path, file_info, request.model)
        else:
            raise HTTPException(status_code=400, detail="不支持的文件类型")

        return {
            "code": 200,
            "message": "检测完成",
            "data": {
                "results": [result]
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"检测失败: {str(e)}"
        )

@router.post("/detectfloder")
def handle_all_files_detect(request: ProcessRequest):
    processed_files = []
    failed_files = []
    for i, file_url in enumerate(request.file_urls):
        try:
            file_path = WORK / file_url.lstrip("/")
            if not file_path.exists():
                failed_files.append({"index": i, "url": file_url, "error": "文件不存在"})
                continue
            if file_path.suffix.lower() not in [".jpg", ".jpeg", ".png"]:
                failed_files.append({"index": i, "url": file_url, "error": "不支持的文件类型"})
                continue
            img = cv2.imread(str(file_path))

            if img is None:
                failed_files.append({"index": i, "url": file_url, "error": "无法读取图像文件"})
                continue
            image_type = classify_image_type(img)
            detections, stats, bboxes = _run_image_detection(img,image_type,request.detection)
            result_path = _save_detection_visual(img, bboxes, file_path.stem)
            relative_path = result_path.relative_to(WORK)
            origin_relative_path = file_path.relative_to(WORK)
            processed_files.append({
                "index": i,
                "original_url": file_url,
                "processed_url": f"{relative_path}",
                "origin_image": str(origin_relative_path).replace('\\', '/'),
                "has_detections": len(detections) > 0,
                "detection_data": {"detections": detections, "statistics": stats}
            })
        except Exception as e:
            failed_files.append({"index": i, "url": file_url, "error": str(e)})

    return {
        "success": True,
        "processed_files": processed_files,
        "failed_files": failed_files,
        "total_files": len(request.file_urls),
        "processed_count": len(processed_files),
        "failed_count": len(failed_files),
        "current_index": request.current_index,
        "result_url": processed_files[request.current_index]["processed_url"] if processed_files and request.current_index < len(processed_files) else None
    }

# 新的预处理端点
@router.post("/preprocess")
async def preprocess_data(request: PreprocessRequest):
    """
    预处理文件
    接受file_type和file_uuid参数，返回预处理结果
    """
    try:
        print(f"收到预处理请求: file_type={request.file_type}, file_uuid={request.file_uuid}, method={request.method}")

        # 检查文件是否存在
        if request.file_uuid not in FILE_DATABASE:
            raise HTTPException(status_code=404, detail="文件不存在")

        file_info = FILE_DATABASE[request.file_uuid]
        file_path = WORK / file_info.file_path

        print(f"文件信息: 原始名称={file_info.original_name}, 文件类型={file_info.file_type}, 路径={file_path}")

        if not file_path.exists():
            raise HTTPException(status_code=404, detail="文件不存在")

        # 根据文件类型和预处理方法执行处理
        if file_info.file_type == 'image':
            result = await preprocess_image_file(file_path, request.method, file_info)
        elif file_info.file_type == 'video':
            result = await preprocess_video_file(file_path, request.method, file_info)
        else:
            raise HTTPException(status_code=400, detail="不支持的文件类型")

        # 更新文件信息中的预处理结果
        file_info.processed_files[request.method] = result["processed_path"]

        return {
            "status": "success",
            "message": "预处理完成",
            "data": {
                "file_info": {
                    "uuid": file_info.uuid,
                    "original_name": file_info.original_name,
                    "processed_path": result["processed_path"],
                    "method": request.method
                }
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"预处理失败: {str(e)}"
        )

async def preprocess_image_file(file_path: pathlib.Path, method: str, file_info: FileInfo):
    """预处理图像文件"""
    print(f"开始预处理图像文件: {file_path}, 方法: {method}")

    # 读取原始图像
    original_image = cv2.imread(str(file_path))
    if original_image is None:
        raise HTTPException(status_code=400, detail="无法读取图像文件")

    print(f"原始图像尺寸: {original_image.shape}")

    # 执行预处理
    processed_image = original_image.copy()
    if method in PREPROCESS_FUNCTIONS_IMAGE:
        func_name = PREPROCESS_FUNCTIONS_IMAGE[method]
        print(f"使用预处理函数: {func_name}")
        if func_name in globals():
            try:
                processed_image = globals()[func_name](processed_image)
                print(f"预处理完成，处理后图像尺寸: {processed_image.shape}")
            except Exception as e:
                print(f"预处理函数执行失败: {e}")
                # 如果预处理失败，使用原始图像
                processed_image = original_image.copy()
        else:
            print(f"预处理函数 {func_name} 未找到，使用原始图像")
    else:
        print(f"不支持的预处理方法: {method}，使用原始图像")

    # 保存处理后的图像
    save_dir = PREPROCESSED_DIR / PREPROCESS_SUBDIRS.get(method, method)
    save_dir.mkdir(parents=True, exist_ok=True)

    result_filename = f"preprocessed_{file_info.uuid}_{method}_{int(time.time())}.jpg"
    result_path = save_dir / result_filename

    cv2.imwrite(str(result_path), processed_image)

    # 返回相对路径
    relative_path = result_path.relative_to(WORK)
    return {
        "processed_path": str(relative_path).replace('\\', '/'),
        "save_path": str(result_path)
    }

async def preprocess_video_file(file_path: pathlib.Path, method: str, file_info: FileInfo):
    """预处理视频文件"""
    print(f"开始预处理视频文件: {file_path}, 方法: {method}")

    # 视频预处理
    if method in PREPROCESS_FUNCTIONS_VIDEO:
        func_name = PREPROCESS_FUNCTIONS_VIDEO[method]
        print(f"使用视频预处理函数: {func_name}")
        if func_name in globals():
            try:
                result_path = globals()[func_name](str(file_path))
                relative_path = pathlib.Path(result_path).relative_to(WORK)
                print(f"视频预处理完成，结果路径: {relative_path}")
                return {
                    "processed_path": str(relative_path).replace('\\', '/'),
                    "save_path": str(result_path)
                }
            except Exception as e:
                print(f"视频预处理函数执行失败: {e}")
                # 如果预处理失败，返回原文件
        else:
            print(f"视频预处理函数 {func_name} 未找到")
    else:
        print(f"不支持的视频预处理方法: {method}")

    # 如果没有对应的预处理方法，返回原文件
    relative_path = file_path.relative_to(WORK)
    return {
        "processed_path": str(relative_path).replace('\\', '/'),
        "save_path": str(file_path)
    }

# 文件信息查询端点
@router.get("/file/{file_uuid}")
async def get_file_info(file_uuid: str):
    """获取文件信息"""
    if file_uuid not in FILE_DATABASE:
        raise HTTPException(status_code=404, detail="文件不存在")

    file_info = FILE_DATABASE[file_uuid]
    return {
        "status": "success",
        "data": {
            "uuid": file_info.uuid,
            "original_name": file_info.original_name,
            "file_path": file_info.file_path,
            "file_type": file_info.file_type,
            "upload_time": file_info.upload_time,
            "file_size": file_info.file_size,
            "processed_files": file_info.processed_files,
            "image_type": file_info.image_type
        }
    }

# 文件列表端点
@router.get("/files")
async def list_files():
    """获取所有文件列表"""
    files = []
    for file_uuid, file_info in FILE_DATABASE.items():
        files.append({
            "uuid": file_info.uuid,
            "original_name": file_info.original_name,
            "file_path": file_info.file_path,
            "file_type": file_info.file_type,
            "upload_time": file_info.upload_time,
            "file_size": file_info.file_size,
            "processed_files": file_info.processed_files,
            "image_type": file_info.image_type
        })

    return {
        "status": "success",
        "data": {
            "files": files,
            "total": len(files)
        }
    }

# RTSP测试端点
def verify_rtsp_stream(url: str) -> bool:
    """验证RTSP流是否可访问"""
    cap = cv2.VideoCapture(url)
    if not cap.isOpened():
        return False

    # 尝试读取一帧
    ret, _ = cap.read()
    cap.release()
    return ret


@router.post("/rtsp/test")
async def test_rtsp(rtsp: dict):
    url = rtsp.get("url")
    if not url:
        return {"success": False, "message": "URL不能为空"}

    # 验证RTSP流是否可访问
    success = verify_rtsp_stream(url)
    return {"success": success}


# 帧处理请求模型
class FrameProcessRequest(BaseModel):
    width: int
    height: int

# 视频帧检测端点（用于实时视频检测）
@router.post("/detect-frame")
async def detect_frame(request: dict):
    """
    检测视频帧
    用于前端实时视频检测
    """
    try:
        frame_data = request.get("frame_data")
        timestamp = request.get("timestamp", time.time())

        if not frame_data:
            raise HTTPException(status_code=400, detail="缺少帧数据")

        # 解码base64图像数据
        import base64
        image_data = base64.b64decode(frame_data)
        frame_array = cv2.imdecode(np.frombuffer(image_data, dtype=np.uint8), cv2.IMREAD_COLOR)

        if frame_array is None:
            raise HTTPException(status_code=400, detail="无法解码图像数据")

        # 执行检测
        detections, stats, bboxes = _run_image_detection(frame_array)

        # 保存原始帧图像（不带检测框）
        original_frame_filename = f"original_frame_{int(timestamp)}_{int(time.time())}.jpg"
        original_frame_path = DETECTED_DIR / "frames" / original_frame_filename
        original_frame_path.parent.mkdir(exist_ok=True)
        cv2.imwrite(str(original_frame_path), frame_array)
        original_relative_path = original_frame_path.relative_to(WORK)

        # 保存带检测框的帧图像
        frame_filename = f"frame_{int(timestamp)}_{int(time.time())}"
        detection_frame_path = _save_detection_visual(frame_array, bboxes, frame_filename)
        relative_frame_path = detection_frame_path.relative_to(WORK)

        return {
            "status": "success",
            "data": {
                "timestamp": timestamp,
                "detections": detections,
                "total_detections": len(detections),
                "has_detections": len(detections) > 0,
                "image": str(relative_frame_path).replace('\\', '/'),
                "origin_image": str(original_relative_path).replace('\\', '/')
            }
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"帧检测失败: {str(e)}"
        )

# ==================== 类别管理接口 ====================

@router.post("/create_category", summary="创建新类别")
async def create_category(data: dict):
    """
    创建新类别，支持从检测界面自动创建类别
    """
    try:
        import json
        from pathlib import Path

        category_name = data.get("name")
        parent_id = data.get("parent_id")

        if not category_name:
            raise HTTPException(status_code=400, detail="类别名称不能为空")

        # 读取当前类别树
        tree_file = Path("./conf/treeList.json")
        tree_data = []

        if tree_file.exists():
            try:
                with open(tree_file, 'r', encoding='utf-8') as f:
                    tree_data = json.load(f)
            except:
                tree_data = []

        # 生成新的类别ID
        max_id = 0
        def find_max_id(nodes):
            nonlocal max_id
            for node in nodes:
                if isinstance(node.get('id'), int):
                    max_id = max(max_id, node['id'])
                if node.get('children'):
                    find_max_id(node['children'])

        find_max_id(tree_data)
        new_id = max_id + 1

        # 创建新类别节点
        new_category = {
            "id": new_id,
            "label": category_name,
            "name": category_name,
            "children": []
        }

        # 添加为根类别
        tree_data.append(new_category)

        # 保存更新后的类别树
        tree_file.parent.mkdir(parents=True, exist_ok=True)
        with open(tree_file, 'w', encoding='utf-8') as f:
            json.dump(tree_data, f, ensure_ascii=False, indent=2)

        return {
            "status": "success",
            "data": {
                "id": new_id,
                "name": category_name,
                "label": category_name
            },
            "message": f"类别 '{category_name}' 创建成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建类别失败: {str(e)}")

@router.post("/refresh_sample_library", summary="刷新样本库")
async def refresh_sample_library():
    """
    刷新样本库数据，用于同步检测界面的更改
    """
    try:
        return {
            "status": "success",
            "message": "样本库数据刷新成功"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"刷新样本库失败: {str(e)}")

@router.post("/detect-video-timestamp", summary="基于时间戳检测视频帧")
async def detect_video_timestamp(data: dict):
    """
    简化的视频检测：根据时间戳检测视频帧
    """
    try:
        import cv2
        import os

        video_file = data.get("video_file")
        timestamp = data.get("timestamp", 0)
        image_type = data.get("image_type", "visible")
        file_uuid = data.get("file_uuid")
        input_source = data.get("input_source")
        preprocessed_path = data.get("preprocessed_path")

        print(f"=== 视频时间戳检测 ===")
        print(f"视频文件: {video_file}")
        print(f"时间戳: {timestamp:.2f}s")
        print(f"图像类型: {image_type}")
        print(f"文件UUID: {file_uuid}")
        print(f"输入源: {input_source}")
        print(f"预处理路径: {preprocessed_path}")

        if not video_file:
            raise HTTPException(status_code=400, detail="缺少视频文件名")

        # 查找视频文件路径 - 改进查找逻辑
        video_path = None

        print(f"开始查找视频文件: {video_file}")

        # 首先使用UUID从文件数据库查找
        if file_uuid and file_uuid in FILE_DATABASE:
            file_info = FILE_DATABASE[file_uuid]
            print(f"从文件数据库找到文件信息: {file_info.original_name}")
            
            # 根据输入源选择文件路径
            if input_source == "preprocessed" and preprocessed_path:
                video_path = WORK / preprocessed_path
                print(f"使用预处理文件路径: {video_path}")
            elif input_source == "preprocessed" and file_info.processed_files:
                # 使用最新的预处理文件
                latest_processed = list(file_info.processed_files.values())[-1]
                video_path = WORK / latest_processed
                print(f"使用最新预处理文件: {video_path}")
            else:
                # 使用原始文件
                video_path = WORK / file_info.file_path
                print(f"使用原始文件: {video_path}")

        # 如果UUID查找失败，使用传统方法
        if not video_path or not video_path.exists():
            print(f"UUID查找失败，使用传统方法查找")
            
            # 优先使用预处理路径
            if preprocessed_path:
                preprocessed_full_path = WORK / preprocessed_path
                print(f"尝试预处理路径: {preprocessed_full_path}")
                if preprocessed_full_path.exists():
                    print(f"通过预处理路径找到文件: {preprocessed_full_path}")
                    video_path = preprocessed_full_path

            # 如果预处理路径无效，使用UUID查找
            if not video_path and file_uuid:
                print(f"使用UUID模糊查找: {file_uuid}")
                for search_dir in [UPLOAD_DIR, PREPROCESSED_DIR]:
                    if search_dir.exists():
                        for file_path in search_dir.rglob(f"*{file_uuid}*"):
                            if file_path.is_file():
                                print(f"通过UUID找到文件: {file_path}")
                                video_path = file_path
                                break
                    if video_path:
                        break

        # 扩展搜索目录列表
        if not video_path:
            search_dirs = [
                UPLOAD_DIR,
                UPLOAD_DIR / "video",
                PREPROCESSED_DIR,
                PREPROCESSED_DIR / "video",
                WORK / "upload",
                WORK / "upload" / "video",
                WORK / "preprocessed",
                WORK / "preprocessed" / "video",
                WORK  # 也在工作根目录查找
            ]

            print(f"搜索目录列表:")
            for search_dir in search_dirs:
                print(f"  - {search_dir} (存在: {search_dir.exists() if hasattr(search_dir, 'exists') else 'N/A'})")

            # 在所有可能的目录中查找视频文件
            for search_dir in search_dirs:
                if search_dir.exists():
                    print(f"搜索目录: {search_dir}")

                    # 递归查找所有文件
                    for file_path in search_dir.rglob("*"):
                        if file_path.is_file():
                            # 检查文件名匹配（支持部分匹配和完全匹配）
                            if (video_file == file_path.name or
                                video_file in file_path.name or
                                file_path.name.endswith(video_file)):

                                print(f"找到匹配文件: {file_path}")
                                video_path = file_path
                                break

                    if video_path:
                        break

            # 如果还是没找到，尝试直接路径
            if not video_path:
                direct_path = WORK / video_file
                if direct_path.exists():
                    print(f"通过直接路径找到文件: {direct_path}")
                    video_path = direct_path

        if not video_path or not video_path.exists():
            # 列出所有可能的视频文件供调试
            print("未找到视频文件，列出所有可能的视频文件:")
            for search_dir in search_dirs:
                if search_dir.exists():
                    for file_path in search_dir.rglob("*.mp4"):
                        print(f"  发现视频文件: {file_path}")
                    for file_path in search_dir.rglob("*.avi"):
                        print(f"  发现视频文件: {file_path}")
                    for file_path in search_dir.rglob("*.mov"):
                        print(f"  发现视频文件: {file_path}")

            raise HTTPException(status_code=404, detail=f"视频文件未找到: {video_file}")

        print(f"最终找到视频文件: {video_path}")

        print(f"找到视频文件: {video_path}")

        # 打开视频文件
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            raise HTTPException(status_code=400, detail="无法打开视频文件")

        # 获取视频信息
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = total_frames / fps if fps > 0 else 0

        print(f"视频信息: FPS={fps:.2f}, 总帧数={total_frames}, 时长={duration:.2f}s")

        # 检查时间戳是否有效
        if timestamp < 0 or timestamp > duration:
            cap.release()
            raise HTTPException(status_code=400, detail=f"时间戳超出范围: {timestamp:.2f}s (视频时长: {duration:.2f}s)")

        # 定位到指定时间戳
        frame_number = int(timestamp * fps)
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)

        # 读取帧
        ret, frame = cap.read()
        cap.release()

        if not ret:
            raise HTTPException(status_code=400, detail=f"无法读取时间戳 {timestamp:.2f}s 的视频帧")

        print(f"成功读取帧: 帧号={frame_number}, 尺寸={frame.shape}")
        # 保存帧图像
        save_dir = DETECTED_DIR / DETECTION_SUBDIRS.get("detection", "detection")
        save_dir.mkdir(parents=True, exist_ok=True)
        frame_filename = f"video_timestamp_{timestamp:.2f}s_{int(time.time())}.jpg"
        frame_path = save_dir / frame_filename
        # 执行检测
        detections,stats, bboxes= _run_image_detection(frame, image_type)
        print(f"检测完成，检测到 {len(detections)} 个目标")
        frame_path = _save_detection_visual(frame, bboxes, frame_filename)

        relative_path = frame_path.relative_to(WORK)
        
        # 保存原始帧图像（不带检测框）
        original_frame_filename = f"video_original_{timestamp:.2f}s_{int(time.time())}.jpg"
        original_frame_path = save_dir / original_frame_filename
        cv2.imwrite(str(original_frame_path), frame)
        original_relative_path = original_frame_path.relative_to(WORK)

        result = {
            "status": "success",
            "data": {
                "timestamp": timestamp,
                "detections": detections,
                "total_detections": len(detections),
                "has_detections": len(detections) > 0,
                "image_path": str(relative_path).replace('\\', '/'),
                "origin_image": str(original_relative_path).replace('\\', '/'),
                "frame_number": frame_number,
                "video_info": {
                    "fps": fps,
                    "duration": duration,
                    "total_frames": total_frames
                }
            },
            "message": f"时间戳 {timestamp:.2f}s 检测完成，检测到 {len(detections)} 个目标"
        }

        print(f"=== 检测完成 ===")
        return result

    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"视频时间戳检测失败: {str(e)}"
        print(error_msg)
        import traceback
        print(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=error_msg)

