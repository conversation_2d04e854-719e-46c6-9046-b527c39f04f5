/* CSS变量定义 */
:root {
  /* 主题色 */
  --primary-color: #4dd3ff;
  --primary-dark: #0aa3e0;
  --primary-light: #66d9ff;
  
  /* 背景色 */
  --bg-primary: rgba(6, 23, 59, 0.85);
  --bg-secondary: rgba(6, 23, 59, 0.6);
  --bg-tertiary: rgba(6, 23, 59, 0.4);
  
  /* 边框色 */
  --border-primary: rgba(77, 163, 171, 0.3);
  --border-secondary: rgba(77, 163, 171, 0.2);
  
  /* 文字色 */
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.9);
  --text-muted: rgba(255, 255, 255, 0.6);
  
  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.2);
  --shadow-primary: 0 2px 8px rgba(77, 211, 255, 0.3);
  
  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  
  /* 过渡 */
  --transition-fast: all 0.2s ease;
  --transition-normal: all 0.3s ease;
}