<template>
  <div class="detected">
    <HeaderComponent title="智能判读" :showBackButton="true" @back="goBack" />

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧区域 (50%) -->
      <div class="left-section">
        <!-- 左上窗体 -->
        <div class="window-panel top-panel">
          <div class="window-header">
            <h3 class="window-title">左上窗体标题</h3>
            <div class="operations">
              <el-select v-model="inputType">
                <el-option label="图像" value="image"></el-option>
                <el-option label="视频" value="video"></el-option>
                <el-option label="文件夹" value="files"></el-option>
                <el-option label="实时视频" value="realtime"></el-option>
              </el-select>
            </div>
          </div>
          <div class="window-content">
            <!-- 图像上传模式 -->
            <div v-if="inputType === 'image'" class="preview-container">
              <div v-if="currentFile.url" class="image-preview">
                <img :src="currentFile.url" alt="预览图" class="preview-image">
                <div class="file-info">
                  <span class="file-name">{{ currentFile.name }}</span>
                  <span class="file-size">{{ formatFileSize(currentFile.size) }}</span>
                </div>
              </div>
              <div v-else class="upload-placeholder" @click="triggerFileUpload">
                <div class="upload-icon">📷</div>
                <p>点击上传图片文件</p>
                <p class="upload-hint">支持 JPG、PNG、GIF 格式</p>
              </div>
              <input ref="imageInput" type="file" accept="image/*" @change="handleFileUpload" style="display: none;" />
            </div>

            <!-- 视频上传模式 -->
            <div v-if="inputType === 'video'" class="preview-container">
              <div v-if="currentFile.url" class="video-preview">
                <video :src="currentFile.url" controls class="preview-video" preload="metadata">
                  您的浏览器不支持视频播放
                </video>
                <div class="file-info">
                  <span class="file-name">{{ currentFile.name }}</span>
                  <span class="file-size">{{ formatFileSize(currentFile.size) }}</span>
                </div>
              </div>
              <div v-else class="upload-placeholder" @click="triggerFileUpload">
                <div class="upload-icon">🎥</div>
                <p>点击上传视频文件</p>
                <p class="upload-hint">支持 MP4、AVI、MOV 格式</p>
              </div>
              <input ref="videoInput" type="file" accept="video/*" @change="handleFileUpload" style="display: none;" />
            </div>

            <!-- 文件夹上传模式 -->
            <div v-if="inputType === 'files'" class="preview-container">
              <div v-if="filesList.length > 0" class="files-preview">
                <div class="current-file-preview">
                  <img :src="filesList[currentFileIndex].url" alt="预览图" class="preview-image">
                </div>
                <div class="files-controls">
                  <div class="file-navigation">
                    <button @click="previousFile" :disabled="currentFileIndex === 0" class="nav-btn">‹</button>
                    <span class="file-counter">{{ currentFileIndex + 1 }} / {{ filesList.length }}</span>
                    <button @click="nextFile" :disabled="currentFileIndex === filesList.length - 1"
                      class="nav-btn">›</button>
                  </div>
                  <div class="file-info">
                    <span class="file-name">{{ filesList[currentFileIndex].name }}</span>
                    <span class="file-size">{{ formatFileSize(filesList[currentFileIndex].size) }}</span>
                  </div>
                </div>
              </div>
              <div v-else class="upload-placeholder" @click="triggerFileUpload">
                <div class="upload-icon">📁</div>
                <p>点击选择多张图片</p>
                <p class="upload-hint">支持批量选择图片文件</p>
              </div>
              <input ref="filesInput" type="file" accept="image/*" multiple @change="handleFileUpload"
                style="display: none;" />
            </div>

            <!-- 实时视频模式 -->
            <div v-if="inputType === 'realtime'" class="preview-container">
              <div class="realtime-preview">
                <video ref="realtimeVideo" class="preview-video" autoplay muted>
                  您的浏览器不支持视频播放
                </video>
                <div class="realtime-controls">
                  <button @click="startRealtime" v-if="!isRealtimeActive" class="control-btn start-btn">开始实时预览</button>
                  <button @click="stopRealtime" v-else class="control-btn stop-btn">停止预览</button>
                  <span class="status-indicator" :class="{ active: isRealtimeActive }">
                    {{ isRealtimeActive ? '● 实时中' : '○ 未连接' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="window-footer">
            <span>左上窗体底部</span>
          </div>
        </div>

        <!-- 左下窗体 -->
        <div class="window-panel bottom-panel">
          <div class="window-header">
            <h3 class="window-title">左下窗体标题</h3>
            <div class="operations">
              <el-select>
                <el-option>
                  aaa
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="window-content">
            <p>左下窗体内容区域</p>
          </div>
          <div class="window-footer">
            <span>左下窗体底部</span>
          </div>
        </div>
      </div>

      <!-- 右侧区域 (50%) -->
      <div class="right-section">
        <div class="window-panel full-panel">
          <div class="window-header">
            <h3 class="window-title">右侧窗体标题</h3>
            <div class="operations">
              <el-select>
                <el-option>
                  aaa
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="window-content">
            <p>右侧窗体内容区域</p>
          </div>
          <div class="window-footer">
            <span>右侧窗体底部</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import HeaderComponent from '@/components/header.vue'
import { APIUploadData, APIPreprocessData, APIDetectData, APIAddSamples } from '@/api/api'
import { serverAddress } from '@/api/config'
import { post, upload } from '@/api/request'

export default {
  name: 'DetectedView',
  components: {
    HeaderComponent
  },
  data() {
    return {
      inputType: "image",
      currentFile: {
        name: '',
        size: 0,
        url: ''
      },
      filesList: [],
      currentFileIndex: 0,
      isRealtimeActive: false
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    triggerFileUpload() {
      if (this.inputType === 'image') {
        this.$refs.imageInput.click()
      } else if (this.inputType === 'video') {
        this.$refs.videoInput.click()
      } else if (this.inputType === 'files') {
        this.$refs.filesInput.click()
      }
    },
    async handleFileUpload(event) {
      const files = event.target.files;
      if (!files || files.length === 0) return;

      try {
        if (this.inputType === 'files') {
          await this.handleMutipleFiles(Array.from(files))
        } else {
          await this.handleSingleFile(files[0])
        }
      } catch (error) {
        this.$message("服务器未开启，请联系管理员");
      }
    },
    formatFileSize(size) {
      const units = ['B', 'KB', 'MB', 'GB', 'TB'];
      let i = 0;
      while (size >= 1024 && i < units.length - 1) {
        size /= 1024;
        i++;
      }
      return size.toFixed(2) + ' ' + units[i];
    },
    previousFile() {
      if (this.currentFileIndex > 0) {
        this.currentFileIndex--;
      }
    },
    nextFile() {
      if (this.currentFileIndex < this.filesList.length - 1) {
        this.currentFileIndex++;
      }
    },
    startRealtime() {
      // 实现实时视频预览的逻辑
      this.isRealtimeActive = true;
      // 这里可以添加获取实时视频流的代码
    },
    stopRealtime() {
      // 停止实时视频预览的逻辑
      this.isRealtimeActive = false;
      // 这里可以添加停止获取实时视频流的代码
    }
  }
}
</script>

<style scoped>
@import '@/assets/css/variables.css';

.detected {
  overflow: hidden;
  background-size: cover;
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  background-image: url("../assets/imgs/backimg.png");
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 10px;
  padding: 20px;
  height: calc(100vh - 80px);
  /* 减去header高度 */
}

.left-section {
  width: 50%;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.right-section {
  width: 50%;
  display: flex;
}

.window-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.top-panel {
  flex: 1;
  min-height: 0;
}

.bottom-panel {
  flex: 1;
  min-height: 0;
}

.full-panel {
  flex: 1;
  width: 100%;
}

.window-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.window-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.window-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: white;
}

.window-footer {
  background: #f8f9fa;
  padding: 12px 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  color: #666;
  font-size: 14px;
}

.operations {
  width: 120px;
}

.preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

/* 上传占位符样式 */
.upload-placeholder {
  border: 2px dashed #d1d8e0;
  border-radius: 12px;
  width: 80%;
  height: 80%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #718096;
  background: linear-gradient(135deg, #f8f9fa 0%, #edf2f7 100%);
  cursor: pointer;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

/* 悬浮效果 */
.upload-placeholder:hover {
  border-color: #3498db;
  color: #3498db;
  background: linear-gradient(135deg, #edf7ff 0%, #e1f0ff 100%);
  transform: scale(1.02);
  box-shadow: 0 8px 20px rgba(52, 152, 219, 0.15);
}

.upload-placeholder:hover .upload-icon {
  transform: scale(1.1);
  color: #2980b9;
}

.upload-placeholder:active {
  transform: scale(0.99);
}

.upload-icon {
  font-size: 4.5rem;
  margin-bottom: 20px;
  transition: all 0.4s ease;
  color: #a0aec0;
}
</style>
